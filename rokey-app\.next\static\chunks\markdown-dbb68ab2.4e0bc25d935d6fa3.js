"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5006],{28831:(e,t,c)=>{c.d(t,{Ay:()=>h});var n=c(34093),a=c(94295),o=c(70275),r=c(95155);c(12115);var l=c(9760),s=c(91498),i=c(49518),u=c(86212),p=c(81142);let d=[],m={allowDangerousHtml:!0},g=/^(https?|ircs?|mailto|xmpp)$/i,f=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function h(e){let t=function(e){let t=e.rehypePlugins||d,c=e.remarkPlugins||d,n=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...m}:m;return(0,i.l)().use(l.A).use(c).use(s.Ay,n).use(t)}(e),c=function(e){let t=e.children||"",c=new p.T;return"string"==typeof t?c.value=t:(0,n.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),c}(e);return function(e,t){let c=t.allowedElements,l=t.allowElement,s=t.components,i=t.disallowedElements,p=t.skipHtml,d=t.unwrapDisallowed,m=t.urlTransform||y;for(let e of f)Object.hasOwn(t,e.from)&&(0,n.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return c&&i&&(0,n.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,u.YR)(e,function(e,t,n){if("raw"===e.type&&n&&"number"==typeof t)return p?n.children.splice(t,1):n.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in o.$)if(Object.hasOwn(o.$,t)&&Object.hasOwn(e.properties,t)){let c=e.properties[t],n=o.$[t];(null===n||n.includes(e.tagName))&&(e.properties[t]=m(String(c||""),t,e))}}if("element"===e.type){let a=c?!c.includes(e.tagName):!!i&&i.includes(e.tagName);if(!a&&l&&"number"==typeof t&&(a=!l(e,t,n)),a&&n&&"number"==typeof t)return d&&e.children?n.children.splice(t,1,...e.children):n.children.splice(t,1),t}}),(0,a.H)(e,{Fragment:r.Fragment,components:s,ignoreInvalidStyle:!0,jsx:r.jsx,jsxs:r.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(c),c),e)}function y(e){let t=e.indexOf(":"),c=e.indexOf("?"),n=e.indexOf("#"),a=e.indexOf("/");return -1===t||-1!==a&&t>a||-1!==c&&t>c||-1!==n&&t>n||g.test(e.slice(0,t))?e:""}},67552:(e,t,c)=>{c.d(t,{A:()=>n});let n=["abap","abnf","actionscript","ada","agda","al","antlr4","apacheconf","apex","apl","applescript","aql","arduino","arff","asciidoc","asm6502","asmatmel","aspnet","autohotkey","autoit","avisynth","avro-idl","bash","basic","batch","bbcode","bicep","birb","bison","bnf","brainfuck","brightscript","bro","bsl","c","cfscript","chaiscript","cil","clike","clojure","cmake","cobol","coffeescript","concurnas","coq","cpp","crystal","csharp","cshtml","csp","css-extras","css","csv","cypher","d","dart","dataweave","dax","dhall","diff","django","dns-zone-file","docker","dot","ebnf","editorconfig","eiffel","ejs","elixir","elm","erb","erlang","etlua","excel-formula","factor","false","firestore-security-rules","flow","fortran","fsharp","ftl","gap","gcode","gdscript","gedcom","gherkin","git","glsl","gml","gn","go-module","go","graphql","groovy","haml","handlebars","haskell","haxe","hcl","hlsl","hoon","hpkp","hsts","http","ichigojam","icon","icu-message-format","idris","iecst","ignore","inform7","ini","io","j","java","javadoc","javadoclike","javascript","javastacktrace","jexl","jolie","jq","js-extras","js-templates","jsdoc","json","json5","jsonp","jsstacktrace","jsx","julia","keepalived","keyman","kotlin","kumir","kusto","latex","latte","less","lilypond","liquid","lisp","livescript","llvm","log","lolcode","lua","magma","makefile","markdown","markup-templating","markup","matlab","maxscript","mel","mermaid","mizar","mongodb","monkey","moonscript","n1ql","n4js","nand2tetris-hdl","naniscript","nasm","neon","nevod","nginx","nim","nix","nsis","objectivec","ocaml","opencl","openqasm","oz","parigp","parser","pascal","pascaligo","pcaxis","peoplecode","perl","php-extras","php","phpdoc","plsql","powerquery","powershell","processing","prolog","promql","properties","protobuf","psl","pug","puppet","pure","purebasic","purescript","python","q","qml","qore","qsharp","r","racket","reason","regex","rego","renpy","rest","rip","roboconf","robotframework","ruby","rust","sas","sass","scala","scheme","scss","shell-session","smali","smalltalk","smarty","sml","solidity","solution-file","soy","sparql","splunk-spl","sqf","sql","squirrel","stan","stylus","swift","systemd","t4-cs","t4-templating","t4-vb","tap","tcl","textile","toml","tremor","tsx","tt2","turtle","twig","typescript","typoscript","unrealscript","uorazor","uri","v","vala","vbnet","velocity","verilog","vhdl","vim","visual-basic","warpscript","wasm","web-idl","wiki","wolfram","wren","xeora","xml-doc","xojo","xquery","yaml","yang","zig"]},85830:(e,t,c)=>{c.d(t,{A:()=>j});var n=c(52673),a=c(53771),o=c(11566),r=c(12115),l=c(79630);function s(e,t){var c=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),c.push.apply(c,n)}return c}function i(e){for(var t=1;t<arguments.length;t++){var c=null!=arguments[t]?arguments[t]:{};t%2?s(Object(c),!0).forEach(function(t){(0,o.A)(e,t,c[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(c)):s(Object(c)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(c,t))})}return e}var u={},p=["language","children","style","customStyle","codeTagProps","useInlineStyles","showLineNumbers","showInlineLineNumbers","startingLineNumber","lineNumberContainerStyle","lineNumberStyle","wrapLines","wrapLongLines","lineProps","renderer","PreTag","CodeTag","code","astGenerator"];function d(e,t){var c=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),c.push.apply(c,n)}return c}function m(e){for(var t=1;t<arguments.length;t++){var c=null!=arguments[t]?arguments[t]:{};t%2?d(Object(c),!0).forEach(function(t){(0,o.A)(e,t,c[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(c,t))})}return e}var g=/\n/g;function f(e){var t,c,n,a,o=e.codeString,l=e.codeStyle,s=e.containerStyle,i=e.numberStyle,u=e.startingLineNumber;return r.createElement("code",{style:Object.assign({},l,void 0===s?{float:"left",paddingRight:"10px"}:s)},(c=(t={lines:o.replace(/\n$/,"").split("\n"),style:void 0===i?{}:i,startingLineNumber:u}).lines,n=t.startingLineNumber,a=t.style,c.map(function(e,t){var c=t+n;return r.createElement("span",{key:"line-".concat(t),className:"react-syntax-highlighter-line-number",style:"function"==typeof a?a(c):a},"".concat(c,"\n"))})))}function h(e,t){return{type:"element",tagName:"span",properties:{key:"line-number--".concat(e),className:["comment","linenumber","react-syntax-highlighter-line-number"],style:t},children:[{type:"text",value:e}]}}function y(e,t,c){var n={display:"inline-block",minWidth:"".concat(c.toString().length,".25em"),paddingRight:"1em",textAlign:"right",userSelect:"none"},a="function"==typeof e?e(t):e;return m(m({},n),a)}function v(e){var t=e.children,c=e.lineNumber,n=e.lineNumberStyle,o=e.largestLineNumber,r=e.showInlineLineNumbers,l=e.lineProps,s=void 0===l?{}:l,i=e.className,u=void 0===i?[]:i,p=e.showLineNumbers,d=e.wrapLongLines,g=e.wrapLines,f=void 0!==g&&g?m({},"function"==typeof s?s(c):s):{};if(f.className=f.className?[].concat((0,a.A)(f.className.trim().split(/\s+/)),(0,a.A)(u)):u,c&&r){var v=y(n,c,o);t.unshift(h(c,v))}return d&p&&(f.style=m({display:"flex"},f.style)),{type:"element",tagName:"span",properties:f,children:t}}function b(e){var t=e.rows,c=e.stylesheet,n=e.useInlineStyles;return t.map(function(e,t){return function e(t){var c=t.node,n=t.stylesheet,a=t.style,o=t.useInlineStyles,s=t.key,p=c.properties,d=c.type,m=c.tagName,g=c.value;if("text"===d)return g;if(m){var f,h,y=(f=0,function(t){return f+=1,t.map(function(t,c){return e({node:t,stylesheet:n,useInlineStyles:o,key:"code-segment-".concat(f,"-").concat(c)})})});if(o){var v=Object.keys(n).reduce(function(e,t){return t.split(".").forEach(function(t){e.includes(t)||e.push(t)}),e},[]),b=p.className&&p.className.includes("token")?["token"]:[],w=p.className&&b.concat(p.className.filter(function(e){return!v.includes(e)}));h=i(i({},p),{},{className:w.join(" ")||void 0,style:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=arguments.length>2?arguments[2]:void 0;return(function(e){if(0===e.length||1===e.length)return e;var t,c=e.join(".");return u[c]||(u[c]=0===(t=e.length)||1===t?e:2===t?[e[0],e[1],"".concat(e[0],".").concat(e[1]),"".concat(e[1],".").concat(e[0])]:3===t?[e[0],e[1],e[2],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0])]:t>=4?[e[0],e[1],e[2],e[3],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[2],".").concat(e[3]),"".concat(e[3],".").concat(e[0]),"".concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[0]),"".concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[3],".").concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[2],".").concat(e[1],".").concat(e[0])]:void 0),u[c]})(e.filter(function(e){return"token"!==e})).reduce(function(e,t){return i(i({},e),c[t])},t)}(p.className,Object.assign({},p.style,void 0===a?{}:a),n)})}else h=i(i({},p),{},{className:p.className.join(" ")});var j=y(c.children);return r.createElement(m,(0,l.A)({key:s},h),j)}}({node:e,stylesheet:c,useInlineStyles:n,key:"code-segement".concat(t)})})}function w(e){return e&&void 0!==e.highlightAuto}function j(e,t){return function(c){var o=c.language,l=c.children,s=c.style,i=void 0===s?t:s,u=c.customStyle,d=void 0===u?{}:u,j=c.codeTagProps,N=void 0===j?{className:o?"language-".concat(o):void 0,style:m(m({},i['code[class*="language-"]']),i['code[class*="language-'.concat(o,'"]')])}:j,k=c.useInlineStyles,x=void 0===k||k,O=c.showLineNumbers,S=void 0!==O&&O,L=c.showInlineLineNumbers,P=void 0===L||L,E=c.startingLineNumber,q=void 0===E?1:E,A=c.lineNumberContainerStyle,I=c.lineNumberStyle,T=void 0===I?{}:I,D=c.wrapLines,C=c.wrapLongLines,H=void 0!==C&&C,z=c.lineProps,R=c.renderer,U=c.PreTag,$=void 0===U?"pre":U,G=c.CodeTag,_=void 0===G?"code":G,B=c.code,F=void 0===B?(Array.isArray(l)?l[0]:l)||"":B,V=c.astGenerator,K=(0,n.A)(c,p);V=V||e;var W=S?r.createElement(f,{containerStyle:A,codeStyle:N.style||{},numberStyle:T,startingLineNumber:q,codeString:F}):null,Y=i.hljs||i['pre[class*="language-"]']||{backgroundColor:"#fff"},J=w(V)?"hljs":"prismjs",M=x?Object.assign({},K,{style:Object.assign({},Y,d)}):Object.assign({},K,{className:K.className?"".concat(J," ").concat(K.className):J,style:Object.assign({},d)});if(H?N.style=m({whiteSpace:"pre-wrap"},N.style):N.style=m({whiteSpace:"pre"},N.style),!V)return r.createElement($,M,W,r.createElement(_,N,F));(void 0===D&&R||H)&&(D=!0),R=R||b;var Q=[{type:"text",value:F}],X=function(e){var t=e.astGenerator,c=e.language,n=e.code,a=e.defaultCodeValue;if(w(t)){var o=-1!==t.listLanguages().indexOf(c);return"text"===c?{value:a,language:"text"}:o?t.highlight(c,n):t.highlightAuto(n)}try{return c&&"text"!==c?{value:t.highlight(n,c)}:{value:a}}catch(e){return{value:a}}}({astGenerator:V,language:o,code:F,defaultCodeValue:Q});null===X.language&&(X.value=Q);var Z=X.value.length;1===Z&&"text"===X.value[0].type&&(Z=X.value[0].value.split("\n").length);var ee=Z+q,et=function(e,t,c,n,o,r,l,s,i){var u,p=function e(t){for(var c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=0;o<t.length;o++){var r=t[o];if("text"===r.type)n.push(v({children:[r],className:(0,a.A)(new Set(c))}));else if(r.children){var l=c.concat(r.properties.className);e(r.children,l).forEach(function(e){return n.push(e)})}}return n}(e.value),d=[],m=-1,f=0;function b(e,a){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t||r.length>0?function(e,a){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return v({children:e,lineNumber:a,lineNumberStyle:s,largestLineNumber:l,showInlineLineNumbers:o,lineProps:c,className:r,showLineNumbers:n,wrapLongLines:i,wrapLines:t})}(e,a,r):function(e,t){if(n&&t&&o){var c=y(s,t,l);e.unshift(h(t,c))}return e}(e,a)}for(;f<p.length;)!function(){var e=p[f],t=e.children[0].value;if(t.match(g)){var c=t.split("\n");c.forEach(function(t,a){var o=n&&d.length+r,l={type:"text",value:"".concat(t,"\n")};if(0===a){var s=b(p.slice(m+1,f).concat(v({children:[l],className:e.properties.className})),o);d.push(s)}else if(a===c.length-1){var i=p[f+1]&&p[f+1].children&&p[f+1].children[0],u={type:"text",value:"".concat(t)};if(i){var g=v({children:[u],className:e.properties.className});p.splice(f+1,0,g)}else{var h=b([u],o,e.properties.className);d.push(h)}}else{var y=b([l],o,e.properties.className);d.push(y)}}),m=f}f++}();if(m!==p.length-1){var w=p.slice(m+1,p.length);if(w&&w.length){var j=b(w,n&&d.length+r);d.push(j)}}return t?d:(u=[]).concat.apply(u,d)}(X,D,void 0===z?{}:z,S,P,q,ee,T,H);return r.createElement($,M,r.createElement(_,N,!P&&W,R({rows:et,stylesheet:i,useInlineStyles:x})))}}}}]);