(()=>{var e={};e.id=1806,e.ids=[1806],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46574:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>a});var i=t(96559),n=t(48088),o=t(37719),p=t(32190);async function a(){try{return p.NextResponse.json({success:!0,data:[{id:"professional",name:"Professional",price:29,currency:"USD",interval:"month",features:["Unlimited API requests","Access to 300+ models","Intelligent role routing","Enterprise security","No request limits","Priority support"],popular:!0}]})}catch(e){return p.NextResponse.json({error:"Failed to fetch pricing tiers"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/pricing/tiers/route",pathname:"/api/pricing/tiers",filename:"route",bundlePath:"app/api/pricing/tiers/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\pricing\\tiers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=u;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(46574));module.exports=s})();