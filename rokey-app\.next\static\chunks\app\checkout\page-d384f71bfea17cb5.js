(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{29301:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(95155),s=r(12115),i=r(35695),n=r(52643),o=r(66766),c=r(6874),l=r.n(c);function d(){return(0,a.jsx)(u,{})}function u(){(0,i.useRouter)();let e=(0,i.useSearchParams)(),t=(0,n.u)(),[r,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),[h,m]=(0,s.useState)(null),[f,p]=(0,s.useState)(!1),x=e.get("plan")||"professional",g=e.get("user_id"),b=e.get("email"),y="true"===e.get("signup");(0,s.useEffect)(()=>{fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"COMPONENT_MOUNT",message:"ActualCheckoutContent component is mounting"})}).catch(()=>{}),p(!0)},[]),(0,s.useEffect)(()=>{if(!f)return;localStorage.getItem("pending_signup"),window.debugCheckout=()=>{};let{data:{subscription:e}}=t.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e&&t&&setTimeout(()=>{j()},100)});return j(),()=>e.unsubscribe()},[f]);let j=async()=>{try{await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CHECKOUT_INITIALIZATION",urlParams:{selectedPlan:x,userId:g,email:b,isSignup:y},currentUrl:window.location.href})}).catch(()=>{});let e=null,r=null,a=0;for(;!e&&a<3;){let s=await t.auth.getUser();e=s.data.user,r=s.error,!e&&a<2&&await new Promise(e=>setTimeout(e,1e3)),a++}if(await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_CHECK",hasUser:!!e,authError:null==r?void 0:r.message,userId:null==e?void 0:e.id,userEmail:null==e?void 0:e.email,retryCount:a,maxRetries:3})}).catch(()=>{}),r||!e){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"AUTH_FAILED",error:(null==r?void 0:r.message)||"No user found",userId:g,email:b,hasUserId:!!g,hasEmail:!!b,retryCount:a,redirecting:!0})}).catch(()=>{}),g?u('Please sign in with your account credentials to complete checkout. Click "Try Again" to go to sign in page.'):u('Authentication required. Please sign up first. Click "Try Again" to go to sign up page.');return}m(e);let s=e.user_metadata,i=null==s?void 0:s.payment_status;await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_FOUND",userId:e.id,email:e.email,paymentStatus:i,userMetadata:s})}).catch(()=>{}),await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CALLING_CREATE_CHECKOUT_SESSION",userId:e.id,selectedPlan:x,aboutToCall:!0})}).catch(()=>{});try{await N(e.id,e)}catch(e){throw await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"CREATE_CHECKOUT_SESSION_ERROR",error:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:void 0})}).catch(()=>{}),e}}catch(e){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"ERROR",error:e instanceof Error?e.message:String(e)})}).catch(()=>{}),u("Failed to initialize checkout. Please try again.")}finally{c(!1)}},N=async(e,t)=>{try{var r;let a=t||h,s=(null==a?void 0:a.email)||(null==a||null==(r=a.user_metadata)?void 0:r.email)||b;if(!s)throw Error("User email not found");let i=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:w(x),tier:x,userId:e,userEmail:s,signup:!1})}),n=await i.json();if(!i.ok)throw Error(n.error||"Failed to create checkout session");if(n.url)window.location.href=n.url;else throw Error("No checkout URL received")}catch(e){u(e instanceof Error?e.message:"Failed to start checkout")}},w=e=>{switch(e.toLowerCase()){case"starter":return"price_1RaA5xC97XFBBUvdt12n1i0T";case"professional":default:return"price_1RaABVC97XFBBUvdkZZc1oQB";case"enterprise":return"price_1RaADDC97XFBBUvd7j6OPJj7"}};return f?d?(0,a.jsx)("div",{className:"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Checkout Error"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:d}),(0,a.jsx)(l(),{href:"/auth/signup?plan=".concat(x),className:"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors",children:"Try Again"})]})}):(0,a.jsx)("div",{className:"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center",children:(0,a.jsx)(o.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-10 h-10 object-contain",priority:!0})})}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl border border-gray-100 p-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Setting up your subscription..."}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,a.jsxs)("span",{className:"text-[#ff6b35] font-semibold",children:[x.charAt(0).toUpperCase()+x.slice(1)," Plan"]}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"})]}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(e=>{switch(e.toLowerCase()){case"starter":return"$29";case"professional":default:return"$99";case"enterprise":return"$299"}})(x),"/month"]})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You'll be redirected to Stripe to complete your payment securely."}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"After payment, you'll verify your email and gain access to your dashboard."})]}),(0,a.jsxs)("div",{className:"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),(0,a.jsx)("span",{children:"Secured by Stripe"})]})]})}):(0,a.jsx)("div",{className:"fixed inset-0 bg-white z-[9999] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading checkout..."})]})})}function h(){return(0,a.jsx)(d,{})}},31915:(e,t,r)=>{Promise.resolve().then(r.bind(r,29301))}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(31915)),_N_E=e.O()}]);