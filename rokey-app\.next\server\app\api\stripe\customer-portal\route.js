(()=>{var e={};e.id=7071,e.ids=[7071],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66511:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>v,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>d});var o=t(96559),i=t(48088),u=t(37719),p=t(32190),n=t(64745),a=t(39398);let c=new n.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-02-24.acacia"}),l=(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function d(e){try{let{userId:r}=await e.json();if(!r)return p.NextResponse.json({error:"Missing userId"},{status:400});let{data:t,error:s}=await l.from("subscriptions").select("stripe_customer_id").eq("user_id",r).eq("status","active").single();if(s||!t)return p.NextResponse.json({error:"No active subscription found for user"},{status:404});let o=await c.billingPortal.sessions.create({customer:t.stripe_customer_id,return_url:"https://roukey.online/dashboard/billing"});return p.NextResponse.json({url:o.url})}catch(e){if(e instanceof n.A.errors.StripeError)return p.NextResponse.json({error:`Stripe error: ${e.message}`},{status:400});return p.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stripe/customer-portal/route",pathname:"/api/stripe/customer-portal",filename:"route",bundlePath:"app/api/stripe/customer-portal/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\customer-portal\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:m,serverHooks:v}=x;function h(){return(0,u.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:m})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,4745],()=>t(66511));module.exports=s})();