"use strict";exports.id=2842,exports.ids=[2842],exports.modules={2842:(e,t,s)=>{s.d(t,{trainingDataCache:()=>c});class a{set(e,t,s){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:s})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,s]of this.cache.entries())e-s.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let c=new a;setInterval(()=>{c.cleanup()},6e5)}};