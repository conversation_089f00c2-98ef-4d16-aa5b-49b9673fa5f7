"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5260],{5848:(e,t,n)=>{n.d(t,{d:()=>r});let i={};function r(e,t){let n=t||i;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){var i;if((i=e)&&"object"==typeof i){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return a(e.children,t,n)}return Array.isArray(e)?a(e,t,n):""}function a(e,t,n){let i=[],r=-1;for(;++r<e.length;)i[r]=l(e[r],t,n);return i.join("")}},32323:(e,t,n)=>{n.d(t,{A:()=>i});function i(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}},61741:(e,t,n)=>{n.d(t,{Y:()=>h});var i=n(5848),r=n(17096),l=n(84545),a=n(54059),o=n(33386),s=n(43828),c=n(12904);let u={}.hasOwnProperty;function h(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(N),autolinkProtocol:k,autolinkEmail:k,atxHeading:r(w),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:k,characterReference:k,codeFenced:r(C),codeFencedFenceInfo:h,codeFencedFenceMeta:h,codeIndented:r(C,h),codeText:r(function(){return{type:"inlineCode",value:""}},h),codeTextData:k,data:k,codeFlowValue:k,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:h,definitionLabelString:h,definitionTitleString:h,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(S),hardBreakTrailing:r(S),htmlFlow:r(D,h),htmlFlowData:k,htmlText:r(D,h),htmlTextData:k,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:h,link:r(N),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(A,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(A),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:h,resourceDestinationString:h,resourceTitleString:h,setextHeading:r(w),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:g(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:g(),autolinkEmail:function(e){x.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){x.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:g(),characterEscapeValue:x,characterReferenceMarkerHexadecimal:v,characterReferenceMarkerNumeric:v,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),i=this.data.characterReferenceType;i?(t=(0,l.C)(n,"characterReferenceMarkerNumeric"===i?10:16),this.data.characterReferenceType=void 0):t=(0,s.s)(n);let r=this.stack[this.stack.length-1];r.value+=t},characterReference:function(e){this.stack.pop().position.end=p(e.end)},codeFenced:g(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:x,codeIndented:g(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:g(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:x,data:x,definition:g(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,o.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:g(),hardBreakEscape:g(b),hardBreakTrailing:g(b),htmlFlow:g(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:x,htmlText:g(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:x,image:g(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=(0,a.s)(t),n.identifier=(0,o.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=p(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(k.call(this,e),x.call(this,e))},link:g(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:g(),listOrdered:g(),listUnordered:g(),paragraph:g(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,o.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:g(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:g(),thematicBreak:g()}};!function e(t,n){let i=-1;for(;++i<n.length;){let r=n[i];Array.isArray(r)?e(t,r):function(e,t){let n;for(n in t)if(u.call(t,n))switch(n){case"canContainEols":{let i=t[n];i&&e[n].push(...i);break}case"transforms":{let i=t[n];i&&e[n].push(...i);break}case"enter":case"exit":{let i=t[n];i&&Object.assign(e[n],i)}}}(t,r)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let i={type:"root",children:[]},r={stack:[i],tokenStack:[],config:t,enter:d,exit:y,buffer:h,resume:m,data:n},l=[],a=-1;for(;++a<e.length;)("listOrdered"===e[a][1].type||"listUnordered"===e[a][1].type)&&("enter"===e[a][0]?l.push(a):a=function(e,t,n){let i,r,l,a,o=t-1,s=-1,c=!1;for(;++o<=n;){let t=e[o];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!i||a||s||l||(l=o),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(i){let a=o;for(r=void 0;a--;){let t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;r&&(e[r][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",r=a}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!r||l<r)&&(i._spread=!0),i.end=Object.assign({},r?e[r][1].start:t[1].end),e.splice(r||o,0,["exit",i,t[2]]),o++,n++}if("listItemPrefix"===t[1].type){let r={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};i=r,e.splice(o,0,["enter",r,t[2]]),o++,n++,l=void 0,a=!0}}}return e[t][1]._spread=c,n}(e,l.pop(),a));for(a=-1;++a<e.length;){let n=t[e[a][0]];u.call(n,e[a][1].type)&&n[e[a][1].type].call(Object.assign({sliceSerialize:e[a][2].sliceSerialize},r),e[a][1])}if(r.tokenStack.length>0){let e=r.tokenStack[r.tokenStack.length-1];(e[1]||f).call(r,void 0,e[0])}for(i.position={start:p(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:p(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},a=-1;++a<t.transforms.length;)i=t.transforms[a](i)||i;return i};function r(e,t){return function(n){d.call(this,e(n),n),t&&t.call(this,n)}}function h(){this.stack.push({type:"fragment",children:[]})}function d(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:p(t.start),end:void 0}}function g(e){return function(t){e&&e.call(this,t),y.call(this,t)}}function y(e,t){let n=this.stack.pop(),i=this.tokenStack.pop();if(i)i[0].type!==e.type&&(t?t.call(this,e,i[0]):(i[1]||f).call(this,e,i[0]));else throw Error("Cannot close `"+e.type+"` ("+(0,c.L)({start:e.start,end:e.end})+"): it’s not open");n.position.end=p(e.end)}function m(){return(0,i.d)(this.stack.pop())}function k(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:p(e.start),end:void 0},t.push(n)),this.stack.push(n)}function x(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=p(e.end)}function b(){this.data.atHardBreak=!0}function v(e){this.data.characterReferenceType=e.type}function C(){return{type:"code",lang:null,meta:null,value:""}}function w(){return{type:"heading",depth:0,children:[]}}function S(){return{type:"break"}}function D(){return{type:"html",value:""}}function N(){return{type:"link",title:null,url:"",children:[]}}function A(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(n)((0,r._K)((0,r.qg)(n).document().write((0,r.vk)()(e,t,!0))))}function p(e){return{line:e.line,column:e.column,offset:e.offset}}function f(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+(0,c.L)({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+(0,c.L)({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+(0,c.L)({start:t.start,end:t.end})+") is still open")}},75374:(e,t,n)=>{n.d(t,{_s:()=>v});var i=n(25583);function r(e,t){let n=t.referenceType,i="]";if("collapsed"===n?i+="[]":"full"===n&&(i+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+i}];let r=e.all(t),l=r[0];l&&"text"===l.type?l.value="["+l.value:r.unshift({type:"text",value:"["});let a=r[r.length-1];return a&&"text"===a.type?a.value+=i:r.push({type:"text",value:i}),r}function l(e){let t=e.spread;return null==t?e.children.length>1:t}var a=n(2288),o=n(29796);let s={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",i={};t.lang&&(i.className=["language-"+t.lang]);let r={type:"element",tagName:"code",properties:i,children:[{type:"text",value:n}]};return t.meta&&(r.data={meta:t.meta}),e.patch(t,r),r={type:"element",tagName:"pre",properties:{},children:[r=e.applyData(t,r)]},e.patch(t,r),r},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",l=String(t.identifier).toUpperCase(),a=(0,i.e)(l.toLowerCase()),o=e.footnoteOrder.indexOf(l),s=e.footnoteCounts.get(l);void 0===s?(s=0,e.footnoteOrder.push(l),n=e.footnoteOrder.length):n=o+1,s+=1,e.footnoteCounts.set(l,s);let c={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+a,id:r+"fnref-"+a+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,c);let u={type:"element",tagName:"sup",properties:{},children:[c]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),l=e.definitionById.get(n);if(!l)return r(e,t);let a={src:(0,i.e)(l.url||""),alt:t.alt};null!==l.title&&void 0!==l.title&&(a.title=l.title);let o={type:"element",tagName:"img",properties:a,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:(0,i.e)(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let i={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,i),e.applyData(t,i)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),l=e.definitionById.get(n);if(!l)return r(e,t);let a={href:(0,i.e)(l.url||"")};null!==l.title&&void 0!==l.title&&(a.title=l.title);let o={type:"element",tagName:"a",properties:a,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:(0,i.e)(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let i=e.all(t),r=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,i=-1;for(;!t&&++i<n.length;)t=l(n[i])}return t}(n):l(t),a={},o=[];if("boolean"==typeof t.checked){let e,n=i[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},i.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),a.className=["task-list-item"]}let s=-1;for(;++s<i.length;){let e=i[s];(r||0!==s||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||r?o.push(e):o.push(...e.children)}let c=i[i.length-1];c&&(r||"element"!==c.type||"p"!==c.tagName)&&o.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:a,children:o};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},i=e.all(t),r=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++r<i.length;){let e=i[r];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),i=n.shift(),r=[];if(i){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([i],!0)};e.patch(t.children[0],n),r.push(n)}if(n.length>0){let i={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=(0,a.PW)(t.children[1]),o=(0,a.Y)(t.children[t.children.length-1]);l&&o&&(i.position={start:l,end:o}),r.push(i)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let i=n?n.children:void 0,r=0===(i?i.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,a=l?l.length:t.children.length,o=-1,s=[];for(;++o<a;){let n=t.children[o],i={},a=l?l[o]:void 0;a&&(i.align=a);let c={type:"element",tagName:r,properties:i,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),s.push(c)}let c={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){let n={type:"text",value:(0,o.E)(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:c,yaml:c,definition:c,footnoteDefinition:c};function c(){}var u=n(34093),h=n(66945);function p(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function f(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var d=n(86212);let g={}.hasOwnProperty,y={};function m(e,t){e.position&&(t.position=(0,a.G1)(e))}function k(e,t){let n=t;if(e&&e.data){let t=e.data.hName,i=e.data.hChildren,r=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&r&&Object.assign(n.properties,(0,h.Ay)(r)),"children"in n&&n.children&&null!=i&&(n.children=i)}return n}function x(e,t){let n=[],i=-1;for(t&&n.push({type:"text",value:"\n"});++i<e.length;)i&&n.push({type:"text",value:"\n"}),n.push(e[i]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function b(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function v(e,t){let n=function(e,t){let n=t||y,i=new Map,r=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,i=-1;for(;++i<n.length;){let r=l.one(n[i],e);if(r){if(i&&"break"===n[i-1].type&&(Array.isArray(r)||"text"!==r.type||(r.value=b(r.value)),!Array.isArray(r)&&"element"===r.type)){let e=r.children[0];e&&"text"===e.type&&(e.value=b(e.value))}Array.isArray(r)?t.push(...r):t.push(r)}}}return t},applyData:k,definitionById:i,footnoteById:r,footnoteCounts:new Map,footnoteOrder:[],handlers:{...s,...n.handlers},one:function(e,t){let n=e.type,i=l.handlers[n];if(g.call(l.handlers,n)&&i)return i(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,i=(0,h.Ay)(n);return i.children=l.all(e),i}return(0,h.Ay)(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},i="value"in t&&!(g.call(n,"hProperties")||g.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,i),e.applyData(t,i)})(l,e,t)},options:n,patch:m,wrap:x};return(0,d.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?i:r,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),l=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||p,r=e.options.footnoteBackLabel||f,l=e.options.footnoteLabel||"Footnotes",a=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[],c=-1;for(;++c<e.footnoteOrder.length;){let l=e.footnoteById.get(e.footnoteOrder[c]);if(!l)continue;let a=e.all(l),o=String(l.identifier).toUpperCase(),u=(0,i.e)(o.toLowerCase()),h=0,p=[],f=e.footnoteCounts.get(o);for(;void 0!==f&&++h<=f;){p.length>0&&p.push({type:"text",value:" "});let e="string"==typeof n?n:n(c,h);"string"==typeof e&&(e={type:"text",value:e}),p.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(h>1?"-"+h:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(c,h),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let d=a[a.length-1];if(d&&"element"===d.type&&"p"===d.tagName){let e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...p)}else a.push(...p);let g={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(a,!0)};e.patch(l,g),s.push(g)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:a,properties:{...(0,h.Ay)(o),id:"footnote-label"},children:[{type:"text",value:l}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return l&&((0,u.ok)("children"in a),a.children.push({type:"text",value:"\n"},l)),a}},91277:(e,t,n)=>{n.d(t,{C:()=>er,H:()=>el});var i=n(41974),r=n(34093),l=n(12556),a=n(94472),o=n(57849);let s="phrasing",c=["autolink","link","image","label"];function u(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function h(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function f(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,r.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function d(e){this.config.exit.autolinkEmail.call(this,e)}function g(e){this.exit(e)}function y(e){!function(e,t,n){let i=(0,o.C)((n||{}).ignore||[]),r=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],i=-1;for(;++i<n.length;){var r;let e=n[i];t.push(["string"==typeof(r=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(r),"g"):r,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<r.length;)(0,a.VG)(e,"text",s);function s(e,t){let n,a=-1;for(;++a<t.length;){let e=t[a],r=n?n.children:void 0;if(i(e,r?r.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],i=r[l][0],a=r[l][1],o=0,s=n.children.indexOf(e),c=!1,u=[];i.lastIndex=0;let h=i.exec(e.value);for(;h;){let n=h.index,r={index:h.index,input:h.input,stack:[...t,e]},l=a(...h,r);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?i.lastIndex=n+1:(o!==n&&u.push({type:"text",value:e.value.slice(o,n)}),Array.isArray(l)?u.push(...l):l&&u.push(l),o=n+h[0].length,c=!0),!i.global)break;h=i.exec(e.value)}return c?(o<e.value.length&&u.push({type:"text",value:e.value.slice(o)}),n.children.splice(s,1,...u)):u=[e],s+u.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,m],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,k]],{ignore:["link","linkReference"]})}function m(e,t,n,r,l){let a="";if(!x(l)||(/^w/i.test(t)&&(n=t+n,t="",a="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let o=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")"),l=(0,i.D)(e,"("),a=(0,i.D)(e,")");for(;-1!==r&&l>a;)e+=n.slice(0,r+1),r=(n=n.slice(r+1)).indexOf(")"),a++;return[e,n]}(n+r);if(!o[0])return!1;let s={type:"link",title:null,url:a+t+o[0],children:[{type:"text",value:t+o[0]}]};return o[1]?[s,{type:"text",value:o[1]}]:s}function k(e,t,n,i){return!(!x(i,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function x(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.Ny)(n)||(0,l.es)(n))&&(!t||47!==n)}var b=n(33386);function v(){this.buffer()}function C(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function w(){this.buffer()}function S(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function D(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,r.ok)("footnoteReference"===n.type),n.identifier=(0,b.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function N(e){this.exit(e)}function A(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,r.ok)("footnoteDefinition"===n.type),n.identifier=(0,b.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function I(e){this.exit(e)}function T(e,t,n,i){let r=n.createTracker(i),l=r.move("[^"),a=n.enter("footnoteReference"),o=n.enter("reference");return l+=r.move(n.safe(n.associationId(e),{after:"]",before:l})),o(),a(),l+=r.move("]")}function E(e,t,n){return 0===t?e:L(e,t,n)}function L(e,t,n){return(n?"":"    ")+e}T.peek=function(){return"["};let R=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function F(e){this.enter({type:"delete",children:[]},e)}function B(e){this.exit(e)}function O(e,t,n,i){let r=n.createTracker(i),l=n.enter("strikethrough"),a=r.move("~~");return a+=n.containerPhrasing(e,{...r.current(),before:a,after:"~"}),a+=r.move("~~"),l(),a}function P(e){return e.length}function H(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}O.peek=function(){return"~"},n(18995);function z(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let i=-1;for(;++i<t.length;)if(e.includes(t[i]))return!0;return!1}function _(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function j(e){return"&#x"+e.toString(16).toUpperCase()+";"}var M=n(49535);function V(e,t,n){let i=(0,M.S)(e),r=(0,M.S)(t);return void 0===i?void 0===r?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===r?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===i?void 0===r?{inside:!1,outside:!1}:1===r?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===r?{inside:!1,outside:!1}:1===r?{inside:!0,outside:!1}:{inside:!1,outside:!1}}var U=n(86212),$=n(5848);function q(e,t,n){let i=e.value||"",r="`",l=-1;for(;RegExp("(^|[^`])"+r+"([^`]|$)").test(i);)r+="`";for(/[^ \r\n]/.test(i)&&(/^[ \r\n]/.test(i)&&/[ \r\n]$/.test(i)||/^`|`$/.test(i))&&(i=" "+i+" ");++l<n.unsafe.length;){let e,t=n.unsafe[l],r=n.compilePattern(t);if(t.atBreak)for(;e=r.exec(i);){let t=e.index;10===i.charCodeAt(t)&&13===i.charCodeAt(t-1)&&t--,i=i.slice(0,t)+" "+i.slice(e.index+1)}}return r+i+r}function Q(e,t){let n=(0,$.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}q.peek=function(){return"`"};(0,o.C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let W={inlineCode:q,listItem:function(e,t,n,i){let r=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let a=l.length+1;("tab"===r||"mixed"===r&&(t&&"list"===t.type&&t.spread||e.spread))&&(a=4*Math.ceil(a/4));let o=n.createTracker(i);o.move(l+" ".repeat(a-l.length)),o.shift(a);let s=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,o.current()),function(e,t,n){return t?(n?"":" ".repeat(a))+e:(n?l:l+" ".repeat(a-l.length))+e});return s(),c}};n(54059);function Y(e){let t=e._align;(0,r.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function G(e){this.exit(e),this.data.inTable=void 0}function Z(e){this.enter({type:"tableRow",children:[]},e)}function J(e){this.exit(e)}function K(e){this.enter({type:"tableCell",children:[]},e)}function X(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,ee));let n=this.stack[this.stack.length-1];(0,r.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function ee(e,t){return"|"===t?t:e}function et(e){let t=this.stack[this.stack.length-2];(0,r.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function en(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,r.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let i,r=t.children,l=-1;for(;++l<r.length;){let e=r[l];if("paragraph"===e.type){i=e;break}}i===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function ei(e,t,n,i){let r=e.children[0],l="boolean"==typeof e.checked&&r&&"paragraph"===r.type,a="["+(e.checked?"x":" ")+"] ",o=n.createTracker(i);l&&o.move(a);let s=W.listItem(e,t,n,{...i,...o.current()});return l&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+a})),s}function er(){return[{transforms:[y],enter:{literalAutolink:u,literalAutolinkEmail:h,literalAutolinkHttp:h,literalAutolinkWww:h},exit:{literalAutolink:g,literalAutolinkEmail:d,literalAutolinkHttp:p,literalAutolinkWww:f}},{enter:{gfmFootnoteCallString:v,gfmFootnoteCall:C,gfmFootnoteDefinitionLabelString:w,gfmFootnoteDefinition:S},exit:{gfmFootnoteCallString:D,gfmFootnoteCall:N,gfmFootnoteDefinitionLabelString:A,gfmFootnoteDefinition:I}},{canContainEols:["delete"],enter:{strikethrough:F},exit:{strikethrough:B}},{enter:{table:Y,tableData:K,tableHeader:K,tableRow:Z},exit:{codeText:X,table:G,tableData:J,tableHeader:J,tableRow:J}},{exit:{taskListCheckValueChecked:et,taskListCheckValueUnchecked:et,paragraph:en}}]}function el(e){let t;return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:c},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:c},{character:":",before:"[ps]",after:"\\/",inConstruct:s,notInConstruct:c}]},(t=!1,e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,i,r){let l=i.createTracker(r),a=l.move("[^"),o=i.enter("footnoteDefinition"),s=i.enter("label");return a+=l.move(i.safe(i.associationId(e),{before:a,after:"]"})),s(),a+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),a+=l.move((t?"\n":" ")+i.indentLines(i.containerFlow(e,l.current()),t?L:E))),o(),a},footnoteReference:T},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:R}],handlers:{delete:O}},function(e){let t=e||{},n=t.tableCellPadding,i=t.tablePipeAlign,r=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let i=W.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(i=i.replace(/\|/g,"\\$&")),i},table:function(e,t,n,i){return o(function(e,t,n){let i=e.children,r=-1,l=[],a=t.enter("table");for(;++r<i.length;)l[r]=s(i[r],t,n);return a(),l}(e,n,i),e.align)},tableCell:a,tableRow:function(e,t,n,i){let r=o([s(e,n,i)]);return r.slice(0,r.indexOf("\n"))}}};function a(e,t,n,i){let r=n.enter("tableCell"),a=n.enter("phrasing"),o=n.containerPhrasing(e,{...i,before:l,after:l});return a(),r(),o}function o(e,t){return function(e,t){let n=t||{},i=(n.align||[]).concat(),r=n.stringLength||P,l=[],a=[],o=[],s=[],c=0,u=-1;for(;++u<e.length;){let t=[],i=[],l=-1;for(e[u].length>c&&(c=e[u].length);++l<e[u].length;){var h;let a=null==(h=e[u][l])?"":String(h);if(!1!==n.alignDelimiters){let e=r(a);i[l]=e,(void 0===s[l]||e>s[l])&&(s[l]=e)}t.push(a)}a[u]=t,o[u]=i}let p=-1;if("object"==typeof i&&"length"in i)for(;++p<c;)l[p]=H(i[p]);else{let e=H(i);for(;++p<c;)l[p]=e}p=-1;let f=[],d=[];for(;++p<c;){let e=l[p],t="",i="";99===e?(t=":",i=":"):108===e?t=":":114===e&&(i=":");let r=!1===n.alignDelimiters?1:Math.max(1,s[p]-t.length-i.length),a=t+"-".repeat(r)+i;!1!==n.alignDelimiters&&((r=t.length+r+i.length)>s[p]&&(s[p]=r),d[p]=r),f[p]=a}a.splice(1,0,f),o.splice(1,0,d),u=-1;let g=[];for(;++u<a.length;){let e=a[u],t=o[u];p=-1;let i=[];for(;++p<c;){let r=e[p]||"",a="",o="";if(!1!==n.alignDelimiters){let e=s[p]-(t[p]||0),n=l[p];114===n?a=" ".repeat(e):99===n?e%2?(a=" ".repeat(e/2+.5),o=" ".repeat(e/2-.5)):o=a=" ".repeat(e/2):o=" ".repeat(e)}!1===n.delimiterStart||p||i.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==r)&&(!1!==n.delimiterStart||p)&&i.push(" "),!1!==n.alignDelimiters&&i.push(a),i.push(r),!1!==n.alignDelimiters&&i.push(o),!1!==n.padding&&i.push(" "),(!1!==n.delimiterEnd||p!==c-1)&&i.push("|")}g.push(!1===n.delimiterEnd?i.join("").replace(/ +$/,""):i.join(""))}return g.join("\n")}(e,{align:t,alignDelimiters:i,padding:n,stringLength:r})}function s(e,t,n){let i=e.children,r=-1,l=[],o=t.enter("tableRow");for(;++r<i.length;)l[r]=a(i[r],e,t,n);return o(),l}}(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:ei}}]}}}}]);