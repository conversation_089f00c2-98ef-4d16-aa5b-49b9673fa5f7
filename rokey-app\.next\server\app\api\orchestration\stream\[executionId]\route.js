"use strict";(()=>{var e={};e.id=6884,e.ids=[6884],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48332:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>_});var o={};r.r(o),r.d(o,{GET:()=>d});var s=r(96559),a=r(48088),n=r(37719),i=r(32190),p=r(2507),u=r(68811);async function d(e,{params:t}){let{executionId:r}=await t;if(!r)return i.NextResponse.json({error:"Execution ID is required"},{status:400});let o=await (0,p.createSupabaseServerClientOnRequest)();try{let{data:e,error:t}=await o.from("orchestration_executions").select("*").eq("id",r).single();if(t||!e)return i.NextResponse.json({error:"Orchestration execution not found"},{status:404});let s=new TextEncoder,a=new ReadableStream({start(t){(0,u.Wu)(r,t);let o={id:crypto.randomUUID(),execution_id:r,type:"orchestration_started",timestamp:new Date().toISOString(),data:{message:"\uD83C\uDFAC Connected to AI team orchestration stream",execution:{id:e.id,status:e.status,total_steps:e.total_steps,created_at:e.created_at}}},a=m(o);t.enqueue(s.encode(a)),"pending"!==e.status&&c(r,t,s)},cancel(){(0,u.ZZ)(r)}});return new Response(a,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"}})}catch(e){return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,t,r){let o=await (0,p.createSupabaseServerClientOnRequest)();try{let{data:s,error:a}=await o.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number",{ascending:!0});if(a)return;for(let o of s||[])for(let s of function(e,t){let r=[],o={execution_id:t,step_number:e.step_number,role_id:e.role_id,model_name:e.model_name};return r.push({id:crypto.randomUUID(),...o,type:"step_assigned",timestamp:e.created_at,data:{commentary:`📋 ${e.role_id} specialist assigned to step ${e.step_number}`,step:{number:e.step_number,role:e.role_id,model:e.model_name,prompt:e.prompt.substring(0,100)+"..."}}}),e.started_at&&r.push({id:crypto.randomUUID(),...o,type:"step_started",timestamp:e.started_at,data:{commentary:`🚀 ${e.role_id} is now working on this challenge...`,estimatedDuration:e.duration_ms||45e3}}),"in_progress"===e.status&&r.push({id:crypto.randomUUID(),...o,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`⚡ ${e.role_id} is making excellent progress...`,progress:.6,partialOutput:e.response?e.response.substring(0,200)+"...":null}}),"completed"===e.status&&e.completed_at&&r.push({id:crypto.randomUUID(),...o,type:"step_completed",timestamp:e.completed_at,data:{commentary:`✅ Outstanding work from ${e.role_id}! Moving to next phase.`,output:e.response,duration:e.duration_ms,tokens:{input:e.tokens_in,output:e.tokens_out},cost:e.cost,quality:.9}}),"failed"===e.status&&r.push({id:crypto.randomUUID(),...o,type:"step_failed",timestamp:e.completed_at||new Date().toISOString(),data:{commentary:`❌ ${e.role_id} encountered an issue. Analyzing options...`,error:e.error_message,retryPlan:"Attempting automatic recovery"}}),r}(o,e)){let e=m(s);t.enqueue(r.encode(e)),await new Promise(e=>setTimeout(e,100))}}catch(e){}}function m(e){return`id: ${e.id}
event: ${e.type}
data: ${JSON.stringify(e)}

`}let l=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orchestration/stream/[executionId]/route",pathname:"/api/orchestration/stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:_,serverHooks:y}=l;function g(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:_})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,580,9398,3410,367],()=>r(48332));module.exports=o})();