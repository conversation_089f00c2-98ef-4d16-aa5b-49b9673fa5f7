'use client';

import { useState, useEffect, FormEvent, useCallback, useMemo } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation'; // For accessing route params
import { type NewApiKey, type DisplayApiKey } from '@/types/apiKeys';
import { llmProviders } from '@/config/models'; // Ensure path is correct
import { PREDEFINED_ROLES, Role, getRoleById } from '@/config/roles';
import { ArrowLeftIcon, TrashIcon, Cog6ToothIcon, CheckCircleIcon, ShieldCheckIcon, InformationCircleIcon, CloudArrowDownIcon, PlusCircleIcon, XCircleIcon, PlusIcon, KeyIcon, PencilIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { Tooltip } from 'react-tooltip';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import { useManageKeysPrefetch } from '@/hooks/useManageKeysPrefetch';
import ManageKeysLoadingSkeleton, { CompactManageKeysLoadingSkeleton } from '@/components/ManageKeysLoadingSkeleton';
import { useRoutingSetupPrefetch } from '@/hooks/useRoutingSetupPrefetch';
import { useNavigationSafe } from '@/contexts/NavigationContext';

// Interface for the richer ModelInfo from /api/providers/list-models
interface FetchedModelInfo {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  version?: string;
  family?: string;
  input_token_limit?: number;
  output_token_limit?: number;
  context_window?: number;
  modality?: string;
  provider_id?: string;
  provider_specific?: Record<string, any>;
}

// Type for a single custom API configuration (matching MyModelsPage)
interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
}

interface ApiKeyWithRoles extends DisplayApiKey {
  assigned_roles: Role[];
  // Note: is_default_general_chat_model is already included from DisplayApiKey
}

// Updated: Interface for User-Defined Custom Roles (now global)
interface UserCustomRole {
  id: string; // UUID for the custom role entry itself (database ID)
  // custom_api_config_id: string; // Removed - roles are now global per user
  user_id: string; // Belongs to this user
  role_id: string; // The user-defined short ID (e.g., "my_summarizer"), unique per user
  name: string;    // User-friendly name
  description?: string | null;
  created_at: string;
  updated_at: string;
}

// Extended Role type for combined list in modal
interface DisplayableRole extends Role {
  isCustom?: boolean;
  databaseId?: string; // Actual DB ID (UUID) for custom roles, for delete/edit operations
}

// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label
const PROVIDER_OPTIONS = llmProviders.map(p => ({ value: p.id, label: p.name }));



export default function ConfigDetailsPage() {
  const params = useParams();
  const configId = params.configId as string;

  // Confirmation modal hook
  const confirmation = useConfirmation();

  // Navigation hook with safe context
  const navigationContext = useNavigationSafe();
  const navigateOptimistically = navigationContext?.navigateOptimistically || ((href: string) => {
    window.location.href = href;
  });

  // Prefetch hooks
  const { getCachedData, isCached } = useManageKeysPrefetch();
  const { createHoverPrefetch: createRoutingHoverPrefetch } = useRoutingSetupPrefetch();

  const [configDetails, setConfigDetails] = useState<CustomApiConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState<boolean>(true);
  const [showOptimisticLoading, setShowOptimisticLoading] = useState<boolean>(false);

  // Provider state now stores the slug (p.id)
  const [provider, setProvider] = useState<string>(PROVIDER_OPTIONS[0]?.value || 'openai'); // Stores slug
  const [predefinedModelId, setPredefinedModelId] = useState<string>('');
  const [apiKeyRaw, setApiKeyRaw] = useState<string>('');
  const [label, setLabel] = useState<string>('');
  const [temperature, setTemperature] = useState<number>(1.0);
  const [isSavingKey, setIsSavingKey] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for dynamic model fetching
  const [fetchedProviderModels, setFetchedProviderModels] = useState<FetchedModelInfo[] | null>(null);
  const [isFetchingProviderModels, setIsFetchingProviderModels] = useState<boolean>(false);
  const [fetchProviderModelsError, setFetchProviderModelsError] = useState<string | null>(null);

  const [savedKeysWithRoles, setSavedKeysWithRoles] = useState<ApiKeyWithRoles[]>([]);
  const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = useState<boolean>(true);
  const [isDeletingKey, setIsDeletingKey] = useState<string | null>(null);
  const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = useState<string | null>(null);

  const [editingRolesApiKey, setEditingRolesApiKey] = useState<ApiKeyWithRoles | null>(null);

  // State for editing API keys
  const [editingApiKey, setEditingApiKey] = useState<ApiKeyWithRoles | null>(null);
  const [editTemperature, setEditTemperature] = useState<number>(1.0);
  const [editPredefinedModelId, setEditPredefinedModelId] = useState<string>('');
  const [isSavingEdit, setIsSavingEdit] = useState<boolean>(false);

  // State for User-Defined Custom Roles
  const [userCustomRoles, setUserCustomRoles] = useState<UserCustomRole[]>([]);
  const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = useState<boolean>(false);
  const [userCustomRolesError, setUserCustomRolesError] = useState<string | null>(null);
  const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = useState<boolean>(false);
  const [newCustomRoleId, setNewCustomRoleId] = useState<string>('');
  const [newCustomRoleName, setNewCustomRoleName] = useState<string>('');
  const [newCustomRoleDescription, setNewCustomRoleDescription] = useState<string>('');
  const [isSavingCustomRole, setIsSavingCustomRole] = useState<boolean>(false);
  const [createCustomRoleError, setCreateCustomRoleError] = useState<string | null>(null);
  const [deletingCustomRoleId, setDeletingCustomRoleId] = useState<string | null>(null); // stores the DB ID (UUID) of the custom role



  // Fetch config details with optimistic loading
  const fetchConfigDetails = useCallback(async () => {
    if (!configId) return;

    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.configDetails) {
      console.log(`⚡ [MANAGE KEYS] Using cached config data for: ${configId}`);
      setConfigDetails(cachedData.configDetails);
      setIsLoadingConfig(false);
      return;
    }

    // Show optimistic loading for first-time visits
    if (!isCached(configId)) {
      setShowOptimisticLoading(true);
    }

    setIsLoadingConfig(true);
    setError(null);

    try {
      const res = await fetch(`/api/custom-configs`);
      if (!res.ok) {
        const errData = await res.json();
        throw new Error(errData.error || 'Failed to fetch configurations list');
      }
      const allConfigs: CustomApiConfig[] = await res.json();
      const currentConfig = allConfigs.find(c => c.id === configId);
      if (!currentConfig) throw new Error('Configuration not found in the list.');
      setConfigDetails(currentConfig);
    } catch (err: any) {
      setError(`Error loading model configuration: ${err.message}`);
      setConfigDetails(null);
    } finally {
      setIsLoadingConfig(false);
      setShowOptimisticLoading(false);
    }
  }, [configId, getCachedData, isCached]);

  useEffect(() => {
    fetchConfigDetails();
  }, [fetchConfigDetails]);

  // New: Function to fetch all models from the database with caching
  const fetchModelsFromDatabase = useCallback(async () => {
    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.models) {
      console.log(`⚡ [MANAGE KEYS] Using cached models data for: ${configId}`);
      setFetchedProviderModels(cachedData.models);
      setIsFetchingProviderModels(false);
      return;
    }

    setIsFetchingProviderModels(true);
    setFetchProviderModelsError(null);
    setFetchedProviderModels(null);
    try {
      // The new API doesn't need a specific provider or API key in the body to list models
      const response = await fetch('/api/providers/list-models', {
        method: 'POST', // Still a POST as per the route's definition
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}), // Send empty or dummy body
      });
      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch models from database.');
      }
      if (data.models) {
        setFetchedProviderModels(data.models);
      } else {
        setFetchedProviderModels([]);
      }
    } catch (err: any) {
      setFetchProviderModelsError(`Error fetching models: ${err.message}`);
      setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI
    } finally {
      setIsFetchingProviderModels(false);
    }
  }, [configId, getCachedData]);

  // New: Fetch all models from DB when configId is available (i.e., page is ready)
  useEffect(() => {
    if (configId) { // Ensures we are on the correct page context
      fetchModelsFromDatabase();
    }
  }, [configId, fetchModelsFromDatabase]);

  // Updated: Function to fetch all global custom roles for the authenticated user with caching
  const fetchUserCustomRoles = useCallback(async () => {
    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.userCustomRoles) {
      console.log(`⚡ [MANAGE KEYS] Using cached custom roles data for: ${configId}`);
      setUserCustomRoles(cachedData.userCustomRoles);
      setIsLoadingUserCustomRoles(false);
      return;
    }

    setIsLoadingUserCustomRoles(true);
    setUserCustomRolesError(null);
    try {
      const response = await fetch(`/api/user/custom-roles`); // New global endpoint
      
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json(); // Attempt to parse error as JSON
        } catch (e) {
          // If error response is not JSON, use text or a generic error
          const errorText = await response.text().catch(() => `HTTP error ${response.status}`);
          errorData = { error: errorText };
        }

        const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : `Failed to fetch custom roles (status: ${response.status})`);
        
        if (response.status === 401) {
            setUserCustomRolesError(errorMessage);
        } else {
            throw new Error(errorMessage); // Throw for other errors to be caught by the main catch
        }
        setUserCustomRoles([]); // Clear roles if there was an error handled here
      } else {
        // Only call .json() here if response.ok and body hasn't been read
        const data: UserCustomRole[] = await response.json();
        setUserCustomRoles(data);
        // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try
      }
    } catch (err: any) {
      // This catch handles network errors from fetch() or errors thrown from !response.ok block
      setUserCustomRolesError(err.message);
      setUserCustomRoles([]); // Clear roles on error
    } finally {
      setIsLoadingUserCustomRoles(false);
    }
  }, []);

  // Fetch API keys and their roles for this config with optimistic loading
  const fetchKeysAndRolesForConfig = useCallback(async () => {
    if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available

    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {
      console.log(`⚡ [MANAGE KEYS] Using cached keys data for: ${configId}`);

      // Process cached keys with roles (same logic as below)
      const keysWithRolesPromises = cachedData.apiKeys.map(async (key: any) => {
        const rolesResponse = await fetch(`/api/keys/${key.id}/roles`);
        let assigned_roles: Role[] = [];
        if (rolesResponse.ok) {
          const roleAssignments: Array<{ role_name: string, role_details?: Role }> = await rolesResponse.json();

          assigned_roles = roleAssignments.map(ra => {
            const predefinedRole = getRoleById(ra.role_name);
            if (predefinedRole) return predefinedRole;

            const customRole = userCustomRoles.find(cr => cr.role_id === ra.role_name);
            if (customRole) {
              return {
                id: customRole.role_id,
                name: customRole.name,
                description: customRole.description || undefined
              };
            }
            return null;
          }).filter(Boolean) as Role[];
        }
        return {
          ...key,
          assigned_roles,
          is_default_general_chat_model: cachedData.defaultChatKeyId === key.id,
        };
      });

      const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);
      setSavedKeysWithRoles(resolvedKeysWithRoles);
      setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);
      setIsLoadingKeysAndRoles(false);
      return;
    }

    setIsLoadingKeysAndRoles(true);
    // Preserve config loading errors, clear others
    setError(prev => prev && prev.startsWith('Error loading model configuration:') ? prev : null);
    setSuccessMessage(null);
    try {
      // Fetch all keys for the config
      const keysResponse = await fetch(`/api/keys?custom_config_id=${configId}`);
      if (!keysResponse.ok) { const errorData = await keysResponse.json(); throw new Error(errorData.error || 'Failed to fetch API keys'); }
      const keys: DisplayApiKey[] = await keysResponse.json();

      // Fetch default general chat key
      const defaultKeyResponse = await fetch(`/api/custom-configs/${configId}/default-chat-key`);
      if (!defaultKeyResponse.ok) { /* Do not throw, default might not be set */ console.warn('Failed to fetch default chat key info'); }
      const defaultKeyData: DisplayApiKey | null = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;
      setDefaultGeneralChatKeyId(defaultKeyData?.id || null);

      // For each key, fetch its assigned roles
      const keysWithRolesPromises = keys.map(async (key) => {
        const rolesResponse = await fetch(`/api/keys/${key.id}/roles`);
        let assigned_roles: Role[] = [];
        if (rolesResponse.ok) {
          const roleAssignments: Array<{ role_name: string, role_details?: Role }> = await rolesResponse.json();
          
          assigned_roles = roleAssignments.map(ra => {
            // 1. Check predefined roles
            const predefinedRole = getRoleById(ra.role_name);
            if (predefinedRole) return predefinedRole;
            
            // 2. Check current user's global custom roles
            const customRole = userCustomRoles.find(cr => cr.role_id === ra.role_name);
            if (customRole) {
              return { 
                id: customRole.role_id, 
                name: customRole.name, 
                description: customRole.description || undefined 
              };
            }
            // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out
            return null;
          }).filter(Boolean) as Role[]; // filter(Boolean) removes null entries
        }
        return {
          ...key,
          assigned_roles,
          is_default_general_chat_model: defaultKeyData?.id === key.id,
        };
      });
      const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);
      setSavedKeysWithRoles(resolvedKeysWithRoles);
    } catch (err: any) {
      setError(prev => prev ? `${prev}; ${err.message}` : err.message); // Append if there was a config load error
    } finally {
      setIsLoadingKeysAndRoles(false);
    }
  }, [configId, userCustomRoles]); // Added userCustomRoles to dependency array



  useEffect(() => {
    if (configDetails) {
      fetchUserCustomRoles(); // Call to fetch custom roles
    }
  }, [configDetails, fetchUserCustomRoles]); // Only depends on configDetails and the stable fetchUserCustomRoles

  // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready
  useEffect(() => {
    // Ensure userCustomRoles is not in its initial undefined/null state from useState([])
    // and actually contains data (or an empty array confirming fetch completion)
    if (configDetails && userCustomRoles) { 
      fetchKeysAndRolesForConfig();
    }
    // This effect runs if configDetails changes, userCustomRoles (state) changes,
    // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).
    // This is the desired behavior: re-fetch keys/roles if custom roles change.
  }, [configDetails, userCustomRoles, fetchKeysAndRolesForConfig]);

  // Updated: Memoize model options based on selected provider and fetched models
  const modelOptions = useMemo(() => {
    if (fetchedProviderModels) {
      const currentProviderDetails = llmProviders.find(p => p.id === provider);
      if (!currentProviderDetails) return [];

      // If the selected provider is "OpenRouter", show all fetched models
      // as an OpenRouter key can access any of them.
      if (currentProviderDetails.id === "openrouter") { // Matched against id for consistency
        return fetchedProviderModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));
        if (deepseekChatModel) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3", // User-friendly name
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));
        if (deepseekReasonerModel) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528", // User-friendly name
            provider_id: "deepseek",
          });
        }
        // If for some reason the specific models are not found in fetchedProviderModels,
        // it's better to return an empty array or a message than all DeepSeek models unfiltered.
        // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.
        // For now, strictly showing only these two if found.
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id
      return fetchedProviderModels
        .filter(model => model.provider_id === currentProviderDetails.id)
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return []; // Return empty array if models haven't been fetched or if fetch failed.
  }, [fetchedProviderModels, provider]);

  // Model options for edit modal - filtered by the current key's provider
  const editModelOptions = useMemo(() => {
    if (fetchedProviderModels && editingApiKey) {
      const currentProviderDetails = llmProviders.find(p => p.id === editingApiKey.provider);
      if (!currentProviderDetails) return [];

      // If the provider is "OpenRouter", show all fetched models
      if (currentProviderDetails.id === "openrouter") {
        return fetchedProviderModels
          .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
          .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // Custom logic for DeepSeek
      if (currentProviderDetails.id === "deepseek") {
        const deepseekOptions: { value: string; label: string; provider_id?: string; }[] = [];
        const deepseekChatModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-chat" && model.provider_id === "deepseek"
        );
        if (deepseekChatModel) {
          deepseekOptions.push({
            value: "deepseek-chat",
            label: "Deepseek V3",
            provider_id: "deepseek",
          });
        }
        const deepseekReasonerModel = fetchedProviderModels.find(
          (model) => model.id === "deepseek-reasoner" && model.provider_id === "deepseek"
        );
        if (deepseekReasonerModel) {
          deepseekOptions.push({
            value: "deepseek-reasoner",
            label: "DeepSeek R1-0528",
            provider_id: "deepseek",
          });
        }
        return deepseekOptions.sort((a, b) => (a.label || '').localeCompare(b.label || ''));
      }

      // For other providers, filter by their specific provider_id
      return fetchedProviderModels
        .filter(model => model.provider_id === currentProviderDetails.id)
        .map(m => ({ value: m.id, label: m.display_name || m.name, provider_id: m.provider_id }))
        .sort((a, b) => (a.label || '').localeCompare(b.label || ''));
    }
    return [];
  }, [fetchedProviderModels, editingApiKey]);

  useEffect(() => {
    // Auto-select the first model from the dynamic modelOptions when provider changes or models load
    if (modelOptions.length > 0) {
      setPredefinedModelId(modelOptions[0].value);
    } else {
      setPredefinedModelId(''); // Clear if no models for provider
    }
  }, [modelOptions, provider]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider

  // Fetch models based on the provider's slug (p.id)
  useEffect(() => {
    if (provider) { // provider is now the slug
      // Logic to fetch models for the selected provider slug might need adjustment
      // if it was previously relying on the provider display name.
      // Assuming fetchProviderModels is adapted or already uses slugs.
      fetchModelsFromDatabase(); 
    }
  }, [provider, fetchModelsFromDatabase]);

  const handleSaveKey = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!configId) { setError('Configuration ID is missing.'); return; }

    // Frontend validation: Check for duplicate models
    const isDuplicateModel = savedKeysWithRoles.some(key => key.predefined_model_id === predefinedModelId);
    if (isDuplicateModel) {
      setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');
      return;
    }

    setIsSavingKey(true); setError(null); setSuccessMessage(null);

    // provider state variable already holds the slug
    const newKeyData: NewApiKey = {
      custom_api_config_id: configId,
      provider,
      predefined_model_id: predefinedModelId,
      api_key_raw: apiKeyRaw,
      label,
      temperature
    };

    // Store previous state for rollback on error
    const previousKeysState = [...savedKeysWithRoles];

    try {
      const response = await fetch('/api/keys', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(newKeyData) });
      const result = await response.json();
      if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');

      // Create optimistic key object with the returned data
      const newKey: ApiKeyWithRoles = {
        id: result.id,
        custom_api_config_id: configId,
        provider,
        predefined_model_id: predefinedModelId,
        label,
        temperature,
        status: 'active',
        created_at: new Date().toISOString(),
        last_used_at: null,
        is_default_general_chat_model: false,
        assigned_roles: []
      };

      // Optimistically add the new key to the list
      setSavedKeysWithRoles(prevKeys => [...prevKeys, newKey]);

      setSuccessMessage(`API key "${label}" saved successfully!`);
      setProvider(PROVIDER_OPTIONS[0]?.value || 'openai');
      setApiKeyRaw('');
      setLabel('');
      setTemperature(1.0);

      // Reset model selection to first available option
      if (modelOptions.length > 0) {
        setPredefinedModelId(modelOptions[0].value);
      }
    } catch (err: any) {
      // Revert UI on error
      setSavedKeysWithRoles(previousKeysState);
      setError(`Save Key Error: ${err.message}`);
    }
    finally { setIsSavingKey(false); }
  };

  const handleEditKey = (key: ApiKeyWithRoles) => {
    setEditingApiKey(key);
    setEditTemperature(key.temperature || 1.0);
    setEditPredefinedModelId(key.predefined_model_id);
  };

  const handleSaveEdit = async () => {
    if (!editingApiKey) return;

    // Frontend validation: Check for duplicate models (excluding the current key being edited)
    const isDuplicateModel = savedKeysWithRoles.some(key =>
      key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId
    );
    if (isDuplicateModel) {
      setError('This model is already configured in this setup. Each model can only be used once per configuration.');
      return;
    }

    setIsSavingEdit(true);
    setError(null);
    setSuccessMessage(null);

    // Store previous state for rollback on error
    const previousKeysState = [...savedKeysWithRoles];

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => {
        if (key.id === editingApiKey.id) {
          return {
            ...key,
            temperature: editTemperature,
            predefined_model_id: editPredefinedModelId
          };
        }
        return key;
      })
    );

    try {
      const response = await fetch(`/api/keys?id=${editingApiKey.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          temperature: editTemperature,
          predefined_model_id: editPredefinedModelId
        })
      });

      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState);
        throw new Error(result.details || result.error || 'Failed to update API key');
      }

      setSuccessMessage(`API key "${editingApiKey.label}" updated successfully!`);
      setEditingApiKey(null);
    } catch (err: any) {
      setError(`Update Key Error: ${err.message}`);
    } finally {
      setIsSavingEdit(false);
    }
  };

  const handleDeleteKey = (keyId: string, keyLabel: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete API Key',
        message: `Are you sure you want to delete the API key "${keyLabel}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,
        confirmText: 'Delete API Key',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setIsDeletingKey(keyId);
        setError(null);
        setSuccessMessage(null);

        // Store previous state for rollback on error
        const previousKeysState = [...savedKeysWithRoles];
        const previousDefaultKeyId = defaultGeneralChatKeyId;
        const keyToDelete = savedKeysWithRoles.find(key => key.id === keyId);

        // Optimistic UI update - immediately remove the key from the list
        setSavedKeysWithRoles(prevKeys => prevKeys.filter(key => key.id !== keyId));

        // If the deleted key was the default, clear the default
        if (keyToDelete?.is_default_general_chat_model) {
          setDefaultGeneralChatKeyId(null);
        }

        try {
          const response = await fetch(`/api/keys/${keyId}`, { method: 'DELETE' });
          const result = await response.json();
          if (!response.ok) {
            // Revert UI on error
            setSavedKeysWithRoles(previousKeysState);
            setDefaultGeneralChatKeyId(previousDefaultKeyId);

            // Special handling for 404 errors (key already deleted)
            if (response.status === 404) {
              // Key was already deleted, so the optimistic update was correct
              // Don't revert the UI, just show a different message
              setSavedKeysWithRoles(prevKeys => prevKeys.filter(key => key.id !== keyId));
              setSuccessMessage(`API key "${keyLabel}" was already deleted.`);
              return; // Don't throw error
            }

            throw new Error(result.details || result.error || 'Failed to delete API key');
          }
          setSuccessMessage(`API key "${keyLabel}" deleted successfully!`);
        } catch (err: any) {
          setError(`Delete Key Error: ${err.message}`);
          throw err; // Re-throw to keep modal open on error
        } finally {
          setIsDeletingKey(null);
        }
      }
    );
  };

  const handleSetDefaultChatKey = async (apiKeyIdToSet: string) => {
    if (!configId) return;
    setError(null); setSuccessMessage(null);
    const previousKeysState = [...savedKeysWithRoles]; // Keep a copy in case of error
    const previousDefaultKeyId = defaultGeneralChatKeyId;

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => ({
        ...key,
        is_default_general_chat_model: key.id === apiKeyIdToSet,
      }))
    );
    setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID

    try {
      const response = await fetch(`/api/custom-configs/${configId}/default-key-handler/${apiKeyIdToSet}`, { method: 'PUT' });
      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState.map(k => ({ ...k }))); // Ensure deep copy for re-render
        setDefaultGeneralChatKeyId(previousDefaultKeyId);
        throw new Error(result.details || result.error || 'Failed to set default chat key');
      }
      setSuccessMessage(result.message || 'Default general chat key updated!');
    } catch (err: any) {
      setError(`Set Default Error: ${err.message}`);
    }
  };

  const handleRoleToggle = async (apiKey: ApiKeyWithRoles, roleId: string, isAssigned: boolean) => {
    setError(null); setSuccessMessage(null);
    const endpoint = `/api/keys/${apiKey.id}/roles`;
    
    // For optimistic update, find role details from combined list (predefined or user's global custom roles)
    const allAvailableRoles: DisplayableRole[] = [
      ...PREDEFINED_ROLES.map(r => ({ ...r, isCustom: false })),
      ...userCustomRoles.map(cr => ({
        id: cr.role_id, 
        name: cr.name,
        description: cr.description || undefined,
        isCustom: true,
        databaseId: cr.id 
      }))
    ];
    const roleDetails = allAvailableRoles.find(r => r.id === roleId) || { id: roleId, name: roleId, description: '' } as DisplayableRole;

    const previousKeysState = savedKeysWithRoles.map(k => ({ 
      ...k, 
      assigned_roles: [...k.assigned_roles.map(r => ({...r}))] 
    })); // Deep copy
    let previousEditingRolesApiKey: ApiKeyWithRoles | null = null;
    if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
      previousEditingRolesApiKey = { ...editingRolesApiKey, assigned_roles: [...editingRolesApiKey.assigned_roles.map(r => ({...r}))] };
    }

    // Optimistic UI update
    setSavedKeysWithRoles(prevKeys =>
      prevKeys.map(key => {
        if (key.id === apiKey.id) {
          const updatedRoles = isAssigned
            ? key.assigned_roles.filter(r => r.id !== roleId)
            : [...key.assigned_roles, roleDetails];
          return { ...key, assigned_roles: updatedRoles };
        }
        return key;
      })
    );

    if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
      setEditingRolesApiKey(prevEditingKey => {
        if (!prevEditingKey) return null;
        const updatedRoles = isAssigned
          ? prevEditingKey.assigned_roles.filter(r => r.id !== roleId)
          : [...prevEditingKey.assigned_roles, roleDetails];
        return { ...prevEditingKey, assigned_roles: updatedRoles };
      });
    }

    try {
      let response;
      if (isAssigned) { // Role is currently assigned, so we want to unassign (DELETE)
        response = await fetch(`${endpoint}/${roleId}`, { method: 'DELETE' });
      } else { // Role is not assigned, so we want to assign (POST)
        response = await fetch(endpoint, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ role_name: roleId }) });
      }
      const result = await response.json();
      if (!response.ok) {
        // Revert UI on error
        setSavedKeysWithRoles(previousKeysState);
        if (previousEditingRolesApiKey) {
          setEditingRolesApiKey(previousEditingRolesApiKey);
        } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {
           const originalKeyData = previousKeysState.find(k => k.id === apiKey.id);
           if (originalKeyData) setEditingRolesApiKey(originalKeyData);
        }
        // Use the error message from the backend if available (e.g., for 409 conflict)
        const errorMessage = response.status === 409 && result.error 
                           ? result.error 
                           : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');
        throw new Error(errorMessage);
      }
      setSuccessMessage(result.message || `Role '${roleDetails.name}' ${isAssigned ? 'unassigned' : 'assigned'} successfully.`);
    } catch (err: any) {
      // err.message now contains the potentially more user-friendly message from the backend or a fallback
      setError(`Role Update Error: ${err.message}`); 
    }
  };



  const handleCreateCustomRole = async () => {
    // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.
    // configId is also not needed for creating a global role.
    if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {
      setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');
      return;
    }
    // Check against PREDEFINED_ROLES and the user's existing global custom roles
    if (PREDEFINED_ROLES.some(pr => pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || 
        userCustomRoles.some(cr => cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {
        setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');
        return;
    }
    if (!newCustomRoleName.trim()) {
      setCreateCustomRoleError('Role Name is required.');
      return;
    }
    setCreateCustomRoleError(null);
    setIsSavingCustomRole(true);
    try {
      const response = await fetch(`/api/user/custom-roles`, { // New global endpoint
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role_id: newCustomRoleId.trim(),
          name: newCustomRoleName.trim(),
          description: newCustomRoleDescription.trim(),
        }),
      });
      
      if (!response.ok) {
        // Try to parse the error response as JSON, but fallback if it's not JSON
        let errorResult;
        try {
          errorResult = await response.json();
        } catch (parseError) {
          // If JSON parsing fails, use the response text or a generic status message
          const errorText = await response.text().catch(() => `HTTP status ${response.status}`);
          errorResult = { error: "Server error, could not parse response.", details: errorText };
        }

        let displayError = errorResult.error || 'Failed to create custom role.';
        if (errorResult.details) {
            displayError += ` (Details: ${errorResult.details})`;
        } else if (errorResult.issues) {
            // If Zod issues, format them for better readability
            const issuesString = Object.entries(errorResult.issues)
                .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
                .join('; ');
            displayError += ` (Issues: ${issuesString})`;
        }
        throw new Error(displayError);
      }

      // If response IS ok, then parse the successful JSON response
      const result = await response.json(); 

      setNewCustomRoleId('');
      setNewCustomRoleName('');
      setNewCustomRoleDescription('');
      // setShowCreateCustomRoleForm(false); // User might want to add multiple roles
      fetchUserCustomRoles(); // Refresh the global list
      setSuccessMessage(`Custom role '${result.name}' created successfully! It is now available globally.`);
    } catch (err: any) {
      setCreateCustomRoleError(err.message);
    } finally {
      setIsSavingCustomRole(false);
    }
  };

  const handleDeleteCustomRole = (customRoleDatabaseId: string, customRoleName: string) => {
    // configId is not needed for deleting a global role
    if (!customRoleDatabaseId) return;

    confirmation.showConfirmation(
      {
        title: 'Delete Custom Role',
        message: `Are you sure you want to delete the custom role "${customRoleName}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,
        confirmText: 'Delete Role',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setDeletingCustomRoleId(customRoleDatabaseId);
        setUserCustomRolesError(null);
        setCreateCustomRoleError(null);
        setSuccessMessage(null);
        try {
          const response = await fetch(`/api/user/custom-roles/${customRoleDatabaseId}`, { // New global endpoint
            method: 'DELETE',
          });
          const result = await response.json(); // Try to parse JSON for all responses
          if (!response.ok) {
            throw new Error(result.error || 'Failed to delete custom role');
          }
          // Optimistically remove from the local state
          setUserCustomRoles(prev => prev.filter(role => role.id !== customRoleDatabaseId));
          setSuccessMessage(result.message || `Global custom role "${customRoleName}" deleted successfully.`);

          // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.
          // This ensures the displayed assigned roles for keys on this page are up-to-date.
          if (configId) { // Only if we are on a specific config page
            fetchKeysAndRolesForConfig();
          }
        } catch (err: any) {
          setUserCustomRolesError(`Error deleting role: ${err.message}`);
          throw err; // Re-throw to keep modal open on error
        } finally {
          setDeletingCustomRoleId(null);
        }
      }
    );
  };



  const renderManageRolesModal = () => {
    if (!editingRolesApiKey) return null;

    const combinedRoles: DisplayableRole[] = [
      ...PREDEFINED_ROLES.map(r => ({ ...r, isCustom: false })),
      ...userCustomRoles.map(cr => ({
        id: cr.role_id, // Use user-defined role_id for matching against assigned_roles
        name: cr.name,
        description: cr.description || undefined,
        isCustom: true,
        databaseId: cr.id // The actual DB ID (UUID) for delete operations
      }))
    ].sort((a, b) => a.name.localeCompare(b.name));

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <div className="card w-full max-w-lg max-h-[90vh] flex flex-col">
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Manage Roles for: <span className="text-orange-600">{editingRolesApiKey.label}</span></h2>
            <button onClick={() => { setEditingRolesApiKey(null); setShowCreateCustomRoleForm(false); setCreateCustomRoleError(null); }} className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200">
              <XCircleIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 border-b border-gray-200">
            {userCustomRolesError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <p className="text-red-800 text-sm">Error with custom roles: {userCustomRolesError}</p>
              </div>
            )}

            <div className="flex justify-end mb-4">
              <button
                onClick={() => setShowCreateCustomRoleForm(!showCreateCustomRoleForm)}
                className="btn-primary text-sm inline-flex items-center"
              >
                <PlusCircleIcon className="h-4 w-4 mr-2" />
                {showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'}
              </button>
            </div>

            {showCreateCustomRoleForm && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                <h3 className="text-md font-medium text-gray-900 mb-3">Create New Custom Role for this Configuration</h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="newCustomRoleId" className="block text-sm font-medium text-gray-700 mb-1">Role ID (short, no spaces, max 30 chars)</label>
                    <input
                      type="text" id="newCustomRoleId" value={newCustomRoleId}
                      onChange={(e) => setNewCustomRoleId(e.target.value.replace(/\s/g, ''))}
                      className="form-input"
                      maxLength={30}
                      placeholder="e.g., my_blog_writer"
                    />
                  </div>
                  <div>
                    <label htmlFor="newCustomRoleName" className="block text-sm font-medium text-gray-700 mb-1">Display Name (max 100 chars)</label>
                    <input
                      type="text" id="newCustomRoleName" value={newCustomRoleName}
                      onChange={(e) => setNewCustomRoleName(e.target.value)}
                      className="form-input"
                      maxLength={100}
                      placeholder="e.g., My Awesome Blog Writer"
                    />
                  </div>
                  <div>
                    <label htmlFor="newCustomRoleDescription" className="block text-sm font-medium text-gray-700 mb-1">Description (optional, max 500 chars)</label>
                    <textarea
                      id="newCustomRoleDescription" value={newCustomRoleDescription}
                      onChange={(e) => setNewCustomRoleDescription(e.target.value)}
                      rows={2}
                      className="form-input"
                      maxLength={500}
                      placeholder="Optional: Describe what this role is for..."
                    />
                  </div>
                  {createCustomRoleError && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <p className="text-red-800 text-sm">{createCustomRoleError}</p>
                    </div>
                  )}
                  <button
                    onClick={handleCreateCustomRole}
                    disabled={isSavingCustomRole}
                    className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'}
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="p-6">
            <p className="text-sm font-medium text-gray-700 mb-3">Select roles to assign:</p>
            <div className="overflow-y-auto space-y-2" style={{ maxHeight: 'calc(90vh - 350px)' }}>
              {isLoadingUserCustomRoles && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"></div>
                  <p className="text-gray-600 text-sm ml-2">Loading custom roles...</p>
                </div>
              )}
              {combinedRoles.map(role => {
                const isAssigned = editingRolesApiKey.assigned_roles.some(ar => ar.id === role.id);
                return (
                  <div key={role.id} className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                    isAssigned
                      ? 'bg-orange-50 border-orange-200 shadow-sm'
                      : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                  }`}>
                    <label htmlFor={`role-${role.id}`} className="flex items-center cursor-pointer flex-grow">
                      <input
                        type="checkbox"
                        id={`role-${role.id}`}
                        checked={isAssigned}
                        onChange={() => handleRoleToggle(editingRolesApiKey!, role.id, isAssigned)}
                        className="h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"
                      />
                      <span className={`ml-3 text-sm font-medium ${isAssigned ? 'text-orange-800' : 'text-gray-900'}`}>
                        {role.name}
                      </span>
                      {role.isCustom && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          Custom
                        </span>
                      )}
                    </label>
                    {role.isCustom && role.databaseId && (
                      <button
                        onClick={() => handleDeleteCustomRole(role.databaseId!, role.name)}
                        disabled={deletingCustomRoleId === role.databaseId}
                        className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2"
                        title="Delete this custom role"
                      >
                         {deletingCustomRoleId === role.databaseId ? <Cog6ToothIcon className="h-4 w-4 animate-spin" /> : <TrashIcon className="h-4 w-4" />}
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
            <div className="flex justify-end">
              <button
                  onClick={() => { setEditingRolesApiKey(null); setShowCreateCustomRoleForm(false); setCreateCustomRoleError(null); }}
                  className="btn-secondary"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Main render logic with optimistic loading
  if (showOptimisticLoading && !isCached(configId)) {
    return <ManageKeysLoadingSkeleton />;
  }

  if (isLoadingConfig && !configDetails) {
    return <CompactManageKeysLoadingSkeleton />;
  }

  return (
    <div className="min-h-screen">
      {/* Header Section */}
      <div className="mb-8">
        <button
          onClick={() => navigateOptimistically('/my-models')}
          className="text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform" />
          Back to My API Models
        </button>

        {/* Modern Header Card */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1">
              {configDetails ? (
                <>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <Cog6ToothIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-gray-900">
                        {configDetails.name}
                      </h1>
                      <p className="text-sm text-gray-500 mt-1">Model Configuration</p>
                    </div>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit">
                    <span className="inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    ID: {configDetails.id}
                  </div>
                </>
              ) : error && !isLoadingConfig ? (
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4">
                    <XCircleIcon className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-red-600">Configuration Error</h1>
                    <p className="text-red-500 mt-1">{error.replace("Error loading model configuration: ","")}</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4">
                    <CloudArrowDownIcon className="h-6 w-6 text-gray-400 animate-pulse" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Loading Configuration...</h1>
                    <p className="text-gray-500 mt-1">Please wait while we fetch your model details</p>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {configDetails && (
              <div className="flex flex-col sm:flex-row gap-3">


                {/* Advanced Routing Setup Button */}
                <button
                  onClick={() => navigateOptimistically(`/routing-setup/${configId}?from=model-config`)}
                  className="inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group"
                  {...createRoutingHoverPrefetch(configId)}
                >
                  <Cog6ToothIcon className="h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200" />
                  Advanced Routing Setup
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Status Messages */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in">
            <div className="flex items-center space-x-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600" />
              <p className="text-green-800 font-medium">{successMessage}</p>
            </div>
          </div>
        )}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in">
            <div className="flex items-center space-x-3">
              <XCircleIcon className="h-5 w-5 text-red-600" />
              <p className="text-red-800 font-medium">{error}</p>
            </div>
          </div>
        )}
      </div>

      {configDetails && (
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-8">
          {/* Left Column - Add New API Key Form */}
          <div className="xl:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md">
                  <PlusIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Add API Key</h2>
                  <p className="text-xs text-gray-500">Configure new key</p>
                </div>
              </div>

              <form onSubmit={handleSaveKey} className="space-y-5">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="provider" className="block text-sm font-medium text-gray-700 mb-2">
                      Provider
                    </label>
                    <select
                      id="provider"
                      value={provider}
                      onChange={(e) => { setProvider(e.target.value); }}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm"
                    >
                      {PROVIDER_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="apiKeyRaw" className="block text-sm font-medium text-gray-700 mb-2">
                      API Key
                    </label>
                    <input
                      id="apiKeyRaw"
                      type="password"
                      value={apiKeyRaw}
                      onChange={(e) => setApiKeyRaw(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm"
                      placeholder="Enter your API key"
                    />
                    {/* Info/Error messages for model fetching */}
                    {isFetchingProviderModels && fetchedProviderModels === null && (
                      <p className="mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg">
                        <CloudArrowDownIcon className="h-4 w-4 mr-1 animate-pulse" />
                        Fetching models...
                      </p>
                    )}
                    {fetchProviderModelsError && (
                      <p className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg">{fetchProviderModelsError}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="predefinedModelId" className="block text-sm font-medium text-gray-700 mb-2">
                      Model Variant
                    </label>
                    <select
                      id="predefinedModelId"
                      value={predefinedModelId}
                      onChange={(e) => setPredefinedModelId(e.target.value)}
                      disabled={!modelOptions.length}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      {modelOptions.length > 0 ? (
                        modelOptions.map(m => (
                          <option key={m.value} value={m.value}>{m.label}</option>
                        ))
                      ) : (
                        <option value="" disabled>{fetchedProviderModels === null && isFetchingProviderModels ? "Loading models..." : "Select a provider first"}</option>
                      )}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="label" className="block text-sm font-medium text-gray-700 mb-2">
                      Label
                    </label>
                    <input
                      type="text"
                      id="label"
                      value={label}
                      onChange={(e) => setLabel(e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm"
                      placeholder="e.g., My OpenAI GPT-4o Key #1"
                    />
                  </div>

                  <div>
                    <label htmlFor="temperature" className="block text-sm font-medium text-gray-700 mb-2">
                      Temperature
                      <span className="text-xs text-gray-500 ml-1">(0.0 - 2.0)</span>
                    </label>
                    <div className="space-y-2">
                      <input
                        type="range"
                        id="temperature"
                        min="0"
                        max="2"
                        step="0.1"
                        value={temperature}
                        onChange={(e) => setTemperature(parseFloat(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"
                      />
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">Conservative</span>
                        <div className="flex items-center space-x-2">
                          <input
                            type="number"
                            min="0"
                            max="2"
                            step="0.1"
                            value={temperature}
                            onChange={(e) => setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0)))}
                            className="w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"
                          />
                        </div>
                        <span className="text-xs text-gray-500">Creative</span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative
                      </p>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim()}
                  className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm"
                >
                  {isSavingKey ? (
                    <span className="flex items-center justify-center">
                      <CloudArrowDownIcon className="h-4 w-4 mr-2 animate-pulse" />
                      Saving...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add API Key
                    </span>
                  )}
                </button>
              </form>

              {/* Information about duplicate rules */}
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 mb-1">Key Configuration Rules</h4>
                    <div className="text-xs text-blue-800 space-y-1">
                      <p>✅ <strong>Same API key, different models:</strong> Allowed</p>
                      <p>✅ <strong>Different API keys, same model:</strong> Allowed</p>
                      <p>❌ <strong>Same model twice:</strong> Not allowed in one configuration</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - API Keys Management */}
          <div className="xl:col-span-3">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md">
                  <KeyIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">API Keys & Roles</h2>
                  <p className="text-xs text-gray-500">Manage existing keys</p>
                </div>
              </div>

              {isLoadingKeysAndRoles && (
                <div className="text-center py-8">
                  <CloudArrowDownIcon className="h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse" />
                  <p className="text-gray-600 text-sm">Loading API keys...</p>
                </div>
              )}

              {!isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || (error && error.startsWith("Error loading model configuration:"))) && (
                <div className="text-center py-8">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <KeyIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-1">No API Keys</h3>
                  <p className="text-xs text-gray-500">Add your first key using the form</p>
                </div>
              )}

              {!isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {savedKeysWithRoles.map((key, index) => (
                    <div key={key.id} className="bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in" style={{animationDelay: `${index * 50}ms`}}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-2">
                            <h3 className="text-sm font-semibold text-gray-900 truncate mr-2">{key.label}</h3>
                            {key.is_default_general_chat_model && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0">
                                <ShieldCheckIcon className="h-3 w-3 mr-1" />
                                Default
                              </span>
                            )}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <p className="text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border">
                                {key.provider} ({key.predefined_model_id})
                              </p>
                              <p className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200">
                                Temp: {key.temperature}
                              </p>
                            </div>

                            <div className="flex flex-wrap gap-1">
                              {key.assigned_roles.length > 0 ? (
                                key.assigned_roles.map(role => (
                                  <span key={role.id} className="inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800">
                                    {role.name}
                                  </span>
                                ))
                              ) : (
                                <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border">No roles</span>
                              )}
                            </div>
                          </div>

                          {!key.is_default_general_chat_model && (
                            <button
                              onClick={() => handleSetDefaultChatKey(key.id)}
                              className="text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors"
                              data-tooltip-id="global-tooltip"
                              data-tooltip-content="Set as default chat model"
                            >
                              Set Default
                            </button>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 ml-2 flex-shrink-0">
                          <button
                            onClick={() => handleEditKey(key)}
                            disabled={isDeletingKey === key.id}
                            className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                            data-tooltip-id="global-tooltip"
                            data-tooltip-content="Edit Model & Settings"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => setEditingRolesApiKey(key)}
                            disabled={isDeletingKey === key.id}
                            className="p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50"
                            data-tooltip-id="global-tooltip"
                            data-tooltip-content="Manage Roles"
                          >
                            <Cog6ToothIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteKey(key.id, key.label)}
                            disabled={isDeletingKey === key.id}
                            className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                            data-tooltip-id="global-tooltip"
                            data-tooltip-content="Delete Key"
                          >
                            {isDeletingKey === key.id ? (
                              <TrashIcon className="h-4 w-4 animate-pulse" />
                            ) : (
                              <TrashIcon className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

            {!isLoadingKeysAndRoles && error && !error.startsWith("Error loading model configuration:") && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <XCircleIcon className="h-5 w-5 text-red-600" />
                  <p className="text-red-800 font-medium text-sm">Could not load API keys/roles: {error}</p>
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      )}

      {/* Modal for Editing Roles */}
      {editingRolesApiKey && renderManageRolesModal()}

      {/* Modal for Editing API Key */}
      {editingApiKey && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="card w-full max-w-lg">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Edit API Key</h2>
              <button
                onClick={() => setEditingApiKey(null)}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{editingApiKey.label}</h3>
                <p className="text-sm text-gray-600">
                  Current: {editingApiKey.provider} ({editingApiKey.predefined_model_id})
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Provider
                  </label>
                  <div className="w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700">
                    {llmProviders.find(p => p.id === editingApiKey.provider)?.name || editingApiKey.provider}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Provider cannot be changed</p>
                </div>

                <div>
                  <label htmlFor="editModelId" className="block text-sm font-medium text-gray-700 mb-2">
                    Model
                  </label>
                  <select
                    id="editModelId"
                    value={editPredefinedModelId}
                    onChange={(e) => setEditPredefinedModelId(e.target.value)}
                    disabled={!editModelOptions.length}
                    className="w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100"
                  >
                    {editModelOptions.length > 0 ? (
                      editModelOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>
                        {isFetchingProviderModels ? 'Loading models...' : 'No models available'}
                      </option>
                    )}
                  </select>
                </div>

                <div>
                  <label htmlFor="editTemperature" className="block text-sm font-medium text-gray-700 mb-2">
                    Temperature: {editTemperature}
                  </label>
                  <input
                    type="range"
                    id="editTemperature"
                    min="0"
                    max="2"
                    step="0.1"
                    value={editTemperature}
                    onChange={(e) => setEditTemperature(parseFloat(e.target.value))}
                    className="slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0.0 (Focused)</span>
                    <span>1.0 (Balanced)</span>
                    <span>2.0 (Creative)</span>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-xs text-gray-600">
                    You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingApiKey(null)}
                  className="btn-secondary"
                  disabled={isSavingEdit}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  disabled={isSavingEdit}
                  className="btn-primary"
                >
                  {isSavingEdit ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {!configDetails && !isLoadingConfig && !error && (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8">
          <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <InformationCircleIcon className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">Model Not Found</h3>
          <p className="text-sm text-gray-600 mb-8">This API Model configuration could not be found or may have been deleted.</p>
          <button
            onClick={() => navigateOptimistically('/my-models')}
            className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Return to My API Models
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />

      <Tooltip id="global-tooltip" />
    </div>
  );
}