"use strict";exports.id=1542,exports.ids=[1542],exports.modules={71542:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var s=r(94735);class o extends s.EventEmitter{constructor(){super(),this.currentColorIndex=0,this.colors=["blue","purple","indigo","cyan","teal","green","yellow","orange","emerald","red"]}static getInstance(){return o.instance||(o.instance=new o),o.instance}getNextColor(){let e=this.colors[this.currentColorIndex%this.colors.length];return this.currentColorIndex++,e}emitProgress(e,t,r){let s={id:crypto.randomUUID(),timestamp:new Date().toISOString(),type:e,message:t,data:r,colorIndex:this.currentColorIndex};this.currentColorIndex++,this.emit("progress",s)}classificationStart(){this.emitProgress("classification_start","\uD83D\uDD0D Multi-role task detected by <PERSON><PERSON><PERSON><PERSON>'s Classifier")}classificationComplete(e,t){this.emitProgress("classification_complete",`✅ Detected ${e.length} roles: ${e.join(", ")}`,{roles:e,threshold:t})}roleSelectionComplete(e,t){this.emitProgress("role_selection",`🎯 Selected ${e.length} roles for orchestration`,{selectedRoles:e,filteredRoles:t})}workflowSelectionComplete(e,t){this.emitProgress("workflow_selection",`🏗️ RouKey Multi-Role System selected ${e} workflow`,{workflowType:e,reasoning:t})}agentCreationStart(){this.emitProgress("agent_creation_start","\uD83E\uDD16 Creating specialized agents...")}agentCreationComplete(e){this.emitProgress("agent_creation_complete",`✅ Created ${e.length} specialized agents`,{agents:e})}supervisorInitStart(){this.emitProgress("supervisor_init","\uD83D\uDC51 Initializing supervisor coordination...")}supervisorInitComplete(e){this.emitProgress("supervisor_init","✅ Supervisor ready for coordination",{supervisorRole:e})}taskPlanningStart(){this.emitProgress("task_planning","\uD83D\uDCCB Planning task distribution...")}taskPlanningComplete(e){this.emitProgress("task_planning","✅ Task distribution planned",{plan:e})}agentWorkStart(e,t){this.emitProgress("agent_work_start",`🚀 ${e} agent starting work...`,{role:e,task:t})}agentWorkComplete(e,t){this.emitProgress("agent_work_complete",`✅ ${e} agent completed work`,{role:e,result:t})}supervisorSynthesisStart(){this.emitProgress("supervisor_synthesis","\uD83D\uDD04 Supervisor synthesizing results...")}supervisorSynthesisComplete(e){this.emitProgress("supervisor_synthesis","✅ Final synthesis complete",{synthesis:e})}orchestrationComplete(e){this.emitProgress("orchestration_complete","\uD83C\uDF89 RouKey Multi-Role orchestration complete!",{result:e})}error(e,t){this.emitProgress("error",`❌ Error in ${e}: ${t}`,{step:e,error:t})}resetColorIndex(){this.currentColorIndex=0}getCurrentColor(){return this.colors[(this.currentColorIndex-1)%this.colors.length]||"blue"}}let i=o}};