(()=>{var e={};e.id=4217,e.ids=[4217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10861:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>o});var u=t(96559),n=t(48088),a=t(37719),i=t(32190);let p=(0,t(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function o(e){try{let e=new Date(Date.now()-36e5).toISOString(),{data:r,error:t}=await p.from("auth.users").select("id, email, created_at, raw_user_meta_data").lt("created_at",e);if(t)return i.NextResponse.json({error:"Failed to query users"},{status:500});let s=r?.filter(e=>e.raw_user_meta_data?.payment_status==="pending")||[],u=[];for(let e of s)try{let{error:r}=await p.auth.admin.deleteUser(e.id);r||u.push(e.email)}catch(e){}return i.NextResponse.json({success:!0,message:`Cleaned up ${u.length} pending users`,deletedUsers:u})}catch(e){return i.NextResponse.json({error:"Cleanup failed"},{status:500})}}async function c(){return i.NextResponse.json({message:"Pending user cleanup endpoint. Use POST to run cleanup.",usage:"POST /api/cleanup/pending-users"})}let d=new u.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cleanup/pending-users/route",pathname:"/api/cleanup/pending-users",filename:"route",bundlePath:"app/api/cleanup/pending-users/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\cleanup\\pending-users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=d;function h(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398],()=>t(10861));module.exports=s})();