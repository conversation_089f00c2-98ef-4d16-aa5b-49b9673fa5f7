(()=>{var e={};e.id=3270,e.ids=[3270],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12785:(e,t,r)=>{Promise.resolve().then(r.bind(r,74994))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22513:(e,t,r)=>{Promise.resolve().then(r.bind(r,29852))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\verify-email\\page.tsx","default")},31455:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>p});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let p={children:["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29852)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\verify-email\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\verify-email\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56878:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(76180),n=r.n(s);function o({className:e="",gridSize:t=40,opacity:r=.1,color:s="#000000",animated:o=!1,glowEffect:l=!1,variant:i="subtle"}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{style:{...(()=>{let e=(e,t)=>"#000000"===e?`rgba(0, 0, 0, ${t})`:"#ffffff"===e?`rgba(255, 255, 255, ${t})`:"#ff6b35"===e?`rgba(255, 107, 53, ${t})`:`${e}${Math.round(255*t).toString(16).padStart(2,"0")}`,a=3.2*r*.8;switch(i){case"tech":return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${e(s,.5*a)} 2px, transparent 2px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${4*t}px ${4*t}px`,animation:o?"tech-grid-move 30s linear infinite":"none",mask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:`
            linear-gradient(${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(${e(s,.7*a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,.7*a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${5*t}px ${5*t}px, ${5*t}px ${5*t}px`,animation:o?"premium-grid-float 40s ease-in-out infinite":"none",mask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,WebkitMaskComposite:"source-in"};default:return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px`,animation:o?"subtle-grid-drift 25s linear infinite":"none",mask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:l?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:n().dynamic([["cdf0235daf430a20",[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t]]])+" "+`absolute inset-0 pointer-events-none ${e}`}),(0,a.jsx)(n(),{id:"cdf0235daf430a20",dynamic:[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t],children:`@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);-moz-transform:translate(${t}px,${t}px);-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}`})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64859:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},74075:e=>{"use strict";e.exports=require("zlib")},74994:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(60687),s=r(43210),n=r(85814),o=r.n(n),l=r(30474),i=r(52535),p=r(64859),d=r(49579),m=r(56878),x=r(79481),c=r(16189);function f(){let[e,t]=(0,s.useState)(!1),[r,n]=(0,s.useState)(""),[f,g]=(0,s.useState)("");(0,c.useSearchParams)(),(0,c.useRouter)();let u=(0,x.u)(),b=async()=>{if(!f)return void n("Please enter your email address");t(!0),n("");try{let{error:e}=await u.auth.resend({type:"signup",email:f});if(e)throw e;n("Verification email sent! Please check your inbox.")}catch(e){n(e.message||"Failed to resend email. Please try again.")}finally{t(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)(m.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,a.jsx)("div",{className:"relative z-10 w-full max-w-md mx-auto",children:(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-3 mb-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,a.jsx)(l.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,a.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:`
                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                `,backgroundSize:"20px 20px"}}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(p.A,{className:"w-10 h-10 text-white"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-black mb-4",children:"Check Your Email"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"We've sent a verification link to:"}),f&&(0,a.jsx)("div",{className:"bg-gray-50 rounded-xl p-4 mb-6",children:(0,a.jsx)("p",{className:"text-[#ff6b35] font-semibold text-lg",children:f})}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"Click the link in the email to verify your account and complete your registration."}),(0,a.jsxs)("div",{className:"space-y-4",children:[!f&&(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"email",placeholder:"Enter your email address",value:f,onChange:e=>g(e.target.value),className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white mb-4"})}),(0,a.jsx)("button",{onClick:b,disabled:e||!f,className:"w-full bg-white border-2 border-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Resend verification email"]})}),r&&(0,a.jsx)("div",{className:`p-4 rounded-xl ${r.includes("sent")?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:(0,a.jsx)("p",{className:`text-sm ${r.includes("sent")?"text-green-600":"text-red-600"}`,children:r})})]}),(0,a.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Already verified?"," ",(0,a.jsx)(o(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Sign in to your account"})]})})]})]})]})})]})}function g(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(f,{})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7482,2535,4912],()=>r(31455));module.exports=a})();