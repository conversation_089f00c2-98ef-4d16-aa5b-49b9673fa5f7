"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7455],{9760:(n,l,e)=>{e.d(l,{A:()=>t});var o=e(61741);function t(n){let l=this;l.parser=function(e){return(0,o.Y)(e,{...l.data("settings"),...n,extensions:l.data("micromarkExtensions")||[],mdastExtensions:l.data("fromMarkdownExtensions")||[]})}}},10985:(n,l,e)=>{function o(){let n=[],l={run:function(...l){let e=-1,o=l.pop();if("function"!=typeof o)throw TypeError("Expected function as last argument, not "+o);!function t(a,...r){let u=n[++e],i=-1;if(a)return void o(a);for(;++i<l.length;)(null===r[i]||void 0===r[i])&&(r[i]=l[i]);l=r,u?(function(n,l){let e;return function(...l){let a,r=n.length>l.length;r&&l.push(o);try{a=n.apply(this,l)}catch(n){if(r&&e)throw n;return o(n)}r||(a&&a.then&&"function"==typeof a.then?a.then(t,o):a instanceof Error?o(a):t(a))};function o(n,...t){e||(e=!0,l(n,...t))}function t(n){o(null,n)}})(u,t)(...r):o(null,...r)}(null,...l)},use:function(e){if("function"!=typeof e)throw TypeError("Expected `middelware` to be a function, not "+e);return n.push(e),l}};return l}e.d(l,{S:()=>o})},14947:(n,l,e)=>{e.d(l,{A:()=>o});function o(n){return n.join(" ").trim()}},29796:(n,l,e)=>{function o(n){let l=String(n),e=/\r?\n|\r/g,o=e.exec(l),a=0,r=[];for(;o;)r.push(t(l.slice(a,o.index),a>0,!0),o[0]),a=o.index+o[0].length,o=e.exec(l);return r.push(t(l.slice(a),a>0,!1)),r.join("")}function t(n,l,e){let o=0,t=n.length;if(l){let l=n.codePointAt(o);for(;9===l||32===l;)o++,l=n.codePointAt(o)}if(e){let l=n.codePointAt(t-1);for(;9===l||32===l;)t--,l=n.codePointAt(t-1)}return t>o?n.slice(o,t):""}e.d(l,{E:()=>o})},31300:(n,l)=>{Object.defineProperty(l,"__esModule",{value:!0}),l.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,o=/-([a-z])/g,t=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,r=/^-(ms)-/,u=function(n,l){return l.toUpperCase()},i=function(n,l){return"".concat(l,"-")};l.camelCase=function(n,l){var s;return(void 0===l&&(l={}),!(s=n)||t.test(s)||e.test(s))?n:(n=n.toLowerCase(),(n=l.reactCompat?n.replace(r,i):n.replace(a,i)).replace(o,u))}},53724:function(n,l,e){var o=(this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}})(e(87924)),t=e(31300);function a(n,l){var e={};return n&&"string"==typeof n&&(0,o.default)(n,function(n,o){n&&o&&(e[(0,t.camelCase)(n,l)]=o)}),e}a.default=a,n.exports=a},63771:(n,l,e)=>{e.d(l,{I6:()=>E,HO:()=>D,qy:()=>z,JW:()=>I});var o={};e.r(o),e.d(o,{boolean:()=>s,booleanish:()=>c,commaOrSpaceSeparated:()=>f,commaSeparated:()=>h,number:()=>d,overloadedBoolean:()=>p,spaceSeparated:()=>g});class t{constructor(n,l,e){this.normal=l,this.property=n,e&&(this.space=e)}}function a(n,l){let e={},o={};for(let l of n)Object.assign(e,l.property),Object.assign(o,l.normal);return new t(e,o,l)}function r(n){return n.toLowerCase()}t.prototype.normal={},t.prototype.property={},t.prototype.space=void 0;class u{constructor(n,l){this.attribute=l,this.property=n}}u.prototype.attribute="",u.prototype.booleanish=!1,u.prototype.boolean=!1,u.prototype.commaOrSpaceSeparated=!1,u.prototype.commaSeparated=!1,u.prototype.defined=!1,u.prototype.mustUseProperty=!1,u.prototype.number=!1,u.prototype.overloadedBoolean=!1,u.prototype.property="",u.prototype.spaceSeparated=!1,u.prototype.space=void 0;let i=0,s=m(),c=m(),p=m(),d=m(),g=m(),h=m(),f=m();function m(){return 2**++i}let y=Object.keys(o);class v extends u{constructor(n,l,e,t){let a=-1;if(super(n,l),function(n,l,e){e&&(n[l]=e)}(this,"space",t),"number"==typeof e)for(;++a<y.length;){let n=y[a];!function(n,l,e){e&&(n[l]=e)}(this,y[a],(e&o[n])===o[n])}}}function k(n){let l={},e={};for(let[o,t]of Object.entries(n.properties)){let a=new v(o,n.transform(n.attributes||{},o),t,n.space);n.mustUseProperty&&n.mustUseProperty.includes(o)&&(a.mustUseProperty=!0),l[o]=a,e[r(o)]=o,e[r(a.attribute)]=o}return new t(l,e,n.space)}v.prototype.defined=!0;let b=k({properties:{ariaActiveDescendant:null,ariaAtomic:c,ariaAutoComplete:null,ariaBusy:c,ariaChecked:c,ariaColCount:d,ariaColIndex:d,ariaColSpan:d,ariaControls:g,ariaCurrent:null,ariaDescribedBy:g,ariaDetails:null,ariaDisabled:c,ariaDropEffect:g,ariaErrorMessage:null,ariaExpanded:c,ariaFlowTo:g,ariaGrabbed:c,ariaHasPopup:null,ariaHidden:c,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:g,ariaLevel:d,ariaLive:null,ariaModal:c,ariaMultiLine:c,ariaMultiSelectable:c,ariaOrientation:null,ariaOwns:g,ariaPlaceholder:null,ariaPosInSet:d,ariaPressed:c,ariaReadOnly:c,ariaRelevant:null,ariaRequired:c,ariaRoleDescription:g,ariaRowCount:d,ariaRowIndex:d,ariaRowSpan:d,ariaSelected:c,ariaSetSize:d,ariaSort:null,ariaValueMax:d,ariaValueMin:d,ariaValueNow:d,ariaValueText:null,role:null},transform:(n,l)=>"role"===l?l:"aria-"+l.slice(4).toLowerCase()});function x(n,l){return l in n?n[l]:l}function C(n,l){return x(n,l.toLowerCase())}let S=k({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:h,acceptCharset:g,accessKey:g,action:null,allow:null,allowFullScreen:s,allowPaymentRequest:s,allowUserMedia:s,alt:null,as:null,async:s,autoCapitalize:null,autoComplete:g,autoFocus:s,autoPlay:s,blocking:g,capture:null,charSet:null,checked:s,cite:null,className:g,cols:d,colSpan:null,content:null,contentEditable:c,controls:s,controlsList:g,coords:d|h,crossOrigin:null,data:null,dateTime:null,decoding:null,default:s,defer:s,dir:null,dirName:null,disabled:s,download:p,draggable:c,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:s,formTarget:null,headers:g,height:d,hidden:p,high:d,href:null,hrefLang:null,htmlFor:g,httpEquiv:g,id:null,imageSizes:null,imageSrcSet:null,inert:s,inputMode:null,integrity:null,is:null,isMap:s,itemId:null,itemProp:g,itemRef:g,itemScope:s,itemType:g,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:s,low:d,manifest:null,max:null,maxLength:d,media:null,method:null,min:null,minLength:d,multiple:s,muted:s,name:null,nonce:null,noModule:s,noValidate:s,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:s,optimum:d,pattern:null,ping:g,placeholder:null,playsInline:s,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:s,referrerPolicy:null,rel:g,required:s,reversed:s,rows:d,rowSpan:d,sandbox:g,scope:null,scoped:s,seamless:s,selected:s,shadowRootClonable:s,shadowRootDelegatesFocus:s,shadowRootMode:null,shape:null,size:d,sizes:null,slot:null,span:d,spellCheck:c,src:null,srcDoc:null,srcLang:null,srcSet:null,start:d,step:null,style:null,tabIndex:d,target:null,title:null,translate:null,type:null,typeMustMatch:s,useMap:null,value:c,width:d,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:g,axis:null,background:null,bgColor:null,border:d,borderColor:null,bottomMargin:d,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:s,declare:s,event:null,face:null,frame:null,frameBorder:null,hSpace:d,leftMargin:d,link:null,longDesc:null,lowSrc:null,marginHeight:d,marginWidth:d,noResize:s,noHref:s,noShade:s,noWrap:s,object:null,profile:null,prompt:null,rev:null,rightMargin:d,rules:null,scheme:null,scrolling:c,standby:null,summary:null,text:null,topMargin:d,valueType:null,version:null,vAlign:null,vLink:null,vSpace:d,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:s,disableRemotePlayback:s,prefix:null,property:null,results:d,security:null,unselectable:null},space:"html",transform:C}),w=k({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:f,accentHeight:d,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:d,amplitude:d,arabicForm:null,ascent:d,attributeName:null,attributeType:null,azimuth:d,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:d,by:null,calcMode:null,capHeight:d,className:g,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:d,diffuseConstant:d,direction:null,display:null,dur:null,divisor:d,dominantBaseline:null,download:s,dx:null,dy:null,edgeMode:null,editable:null,elevation:d,enableBackground:null,end:null,event:null,exponent:d,externalResourcesRequired:null,fill:null,fillOpacity:d,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:h,g2:h,glyphName:h,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:d,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:d,horizOriginX:d,horizOriginY:d,id:null,ideographic:d,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:d,k:d,k1:d,k2:d,k3:d,k4:d,kernelMatrix:f,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:d,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:d,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:d,overlineThickness:d,paintOrder:null,panose1:null,path:null,pathLength:d,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:g,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:d,pointsAtY:d,pointsAtZ:d,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:f,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:f,rev:f,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:f,requiredFeatures:f,requiredFonts:f,requiredFormats:f,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:d,specularExponent:d,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:d,strikethroughThickness:d,string:null,stroke:null,strokeDashArray:f,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:d,strokeOpacity:d,strokeWidth:null,style:null,surfaceScale:d,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:f,tabIndex:d,tableValues:null,target:null,targetX:d,targetY:d,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:f,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:d,underlineThickness:d,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:d,values:null,vAlphabetic:d,vMathematical:d,vectorEffect:null,vHanging:d,vIdeographic:d,version:null,vertAdvY:d,vertOriginX:d,vertOriginY:d,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:d,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:x}),P=k({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(n,l)=>"xlink:"+l.slice(5).toLowerCase()}),L=k({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:C}),M=k({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(n,l)=>"xml:"+l.slice(3).toLowerCase()}),D={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},O=/[A-Z]/g,R=/-[a-z]/g,A=/^data[-\w.:]+$/i;function E(n,l){let e=r(l),o=l,t=u;if(e in n.normal)return n.property[n.normal[e]];if(e.length>4&&"data"===e.slice(0,4)&&A.test(l)){if("-"===l.charAt(4)){let n=l.slice(5).replace(R,U);o="data"+n.charAt(0).toUpperCase()+n.slice(1)}else{let n=l.slice(4);if(!R.test(n)){let e=n.replace(O,T);"-"!==e.charAt(0)&&(e="-"+e),l="data"+e}}t=v}return new t(o,l)}function T(n){return"-"+n.toLowerCase()}function U(n){return n.charAt(1).toUpperCase()}let z=a([b,S,P,L,M],"html"),I=a([b,w,P,L,M],"svg")},87924:function(n,l,e){var o=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(l,"__esModule",{value:!0}),l.default=function(n,l){var e=null;if(!n||"string"!=typeof n)return e;var o=(0,t.default)(n),a="function"==typeof l;return o.forEach(function(n){if("declaration"===n.type){var o=n.property,t=n.value;a?l(o,t,n):t&&((e=e||{})[o]=t)}}),e};var t=o(e(36301))},91498:(n,l,e)=>{e.d(l,{Ay:()=>t});var o=e(75374);function t(n,l){return n&&"run"in n?async function(e,t){let a=(0,o._s)(e,{file:t,...l});await n.run(a,t)}:function(e,t){return(0,o._s)(e,{file:t,...n||l})}}}}]);