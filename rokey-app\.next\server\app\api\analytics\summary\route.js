(()=>{var e={};e.id=2847,e.ids=[1489,2847],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>u});var r=s(34386),o=s(44999);async function u(){let e=await (0,o.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68615:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>k,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{GET:()=>l});var o=s(96559),u=s(48088),a=s(37719),i=s(32190),n=s(2507),p=s(45697);let c=p.z.object({startDate:p.z.string().datetime({offset:!0}).optional(),endDate:p.z.string().datetime({offset:!0}).optional(),customApiConfigId:p.z.string().uuid().optional(),groupBy:p.z.enum(["day","week","month","provider","model"]).optional().default("day")});async function l(e){try{let{searchParams:t}=new URL(e.url),s=Object.fromEntries(t.entries()),{startDate:r,endDate:o,customApiConfigId:u,groupBy:a}=c.parse(s),p=await (0,n.createSupabaseServerClientOnRequest)(),{data:{user:l},error:d}=await p.auth.getUser();if(d||!l)return i.NextResponse.json({error:"Unauthorized: You must be logged in to view analytics."},{status:401});let m=p.from("request_logs").select(`
        request_timestamp,
        status_code,
        cost,
        input_tokens,
        output_tokens,
        llm_provider_name,
        llm_model_name,
        custom_api_config_id
      `).eq("user_id",l.id).not("cost","is",null);r&&(m=m.gte("request_timestamp",r)),o&&(m=m.lte("request_timestamp",o)),u&&(m=m.eq("custom_api_config_id",u));let{data:_,error:k}=await m.order("request_timestamp",{ascending:!1});if(k)return i.NextResponse.json({error:"Failed to fetch analytics data",details:k.message},{status:500});let g=_.length,y=_.filter(e=>e.status_code&&e.status_code<400).length,q=_.reduce((e,t)=>e+(t.cost||0),0),h=_.reduce((e,t)=>e+(t.input_tokens||0),0),f=_.reduce((e,t)=>e+(t.output_tokens||0),0),v=[];if("provider"===a){let e=_.reduce((e,t)=>{let s=t.llm_provider_name||"Unknown";return e[s]||(e[s]={name:s,requests:0,cost:0,input_tokens:0,output_tokens:0,success_rate:0}),e[s].requests+=1,e[s].cost+=t.cost||0,e[s].input_tokens+=t.input_tokens||0,e[s].output_tokens+=t.output_tokens||0,t.status_code&&t.status_code<400&&(e[s].success_rate+=1),e},{});v=Object.values(e).map(e=>({...e,success_rate:e.requests>0?e.success_rate/e.requests*100:0}))}else if("model"===a){let e=_.reduce((e,t)=>{let s=t.llm_model_name||"Unknown";return e[s]||(e[s]={name:s,requests:0,cost:0,input_tokens:0,output_tokens:0,provider:t.llm_provider_name||"Unknown"}),e[s].requests+=1,e[s].cost+=t.cost||0,e[s].input_tokens+=t.input_tokens||0,e[s].output_tokens+=t.output_tokens||0,e},{});v=Object.values(e)}else{let e=_.reduce((e,t)=>{let s,r=new Date(t.request_timestamp);if("day"===a)s=r.toISOString().split("T")[0];else if("week"===a){let e=new Date(r);e.setDate(r.getDate()-r.getDay()),s=e.toISOString().split("T")[0]}else s=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}`;return e[s]||(e[s]={period:s,requests:0,cost:0,input_tokens:0,output_tokens:0}),e[s].requests+=1,e[s].cost+=t.cost||0,e[s].input_tokens+=t.input_tokens||0,e[s].output_tokens+=t.output_tokens||0,e},{});v=Object.values(e).sort((e,t)=>e.period.localeCompare(t.period))}let x={summary:{total_requests:g,successful_requests:y,success_rate:g>0?y/g*100:0,total_cost:q,total_input_tokens:h,total_output_tokens:f,total_tokens:h+f,average_cost_per_request:g>0?q/g:0},grouped_data:v,filters:{start_date:r,end_date:o,custom_api_config_id:u,group_by:a}};return i.NextResponse.json(x)}catch(e){if(e instanceof p.z.ZodError)return i.NextResponse.json({error:"Invalid query parameters",details:e.errors},{status:400});return i.NextResponse.json({error:"Internal server error",details:e.message},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/analytics/summary/route",pathname:"/api/analytics/summary",filename:"route",bundlePath:"app/api/analytics/summary/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\analytics\\summary\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:_,serverHooks:k}=d;function g(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:_})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410,5697],()=>s(68615));module.exports=r})();