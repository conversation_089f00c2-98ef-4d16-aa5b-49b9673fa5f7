"use strict";(()=>{var e={};e.id=6137,e.ids=[6137],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5649:(e,t,s)=>{s.d(t,{y:()=>r});var o=s(68811);class r{constructor(e,t){this.classificationApiKey=e,this.executionId=t}async validateStepOutput(e,t,s,r,i){let n=`As an AI orchestration moderator, evaluate this step output:

Original Request: "${r}"
Role: ${t}
Expected Outcome: ${i}
Actual Output: "${s}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;try{let s=await this.callModerator(n),r=JSON.parse(s);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Validating ${t} output: ${r.reasoning}`,validation:r},e,t),r}catch(e){return{isValid:s.length>50,quality:.7,issues:[],suggestions:[],canProceed:!0}}}async resolveConflicts(e,t){let s=`As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${t}"

Conflicting Outputs:
${e.map((e,t)=>`${t+1}. ${e.roleId} (confidence: ${e.confidence}): "${e.output}"`).join("\n")}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;try{let e=await this.callModerator(s),t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Conflict resolution: ${t.reasoning}`,decision:t}),t}catch(s){let t=e.reduce((e,t)=>t.confidence>e.confidence?t:e);return{action:"proceed",reasoning:`Selected ${t.roleId} output with highest confidence (${t.confidence})`,confidence:.6,nextSteps:["continue_with_selected_output"]}}}async synthesizeOutputs(e,t){await (0,o.Zi)(this.executionId,"synthesis_started",{commentary:"\uD83E\uDDE9 Beginning synthesis of all specialist outputs...",totalSteps:e.length});let s=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${t}"

Specialist Outputs:
${e.map(e=>`${e.stepNumber}. ${e.roleId} (quality: ${e.quality}): "${e.output}"`).join("\n\n")}

Create a comprehensive, well-structured response that:
1. Integrates all valuable insights
2. Maintains logical flow
3. Resolves any contradictions
4. Provides a complete answer to the original request

Respond in JSON format:
{
  "combinedOutput": "the synthesized response",
  "methodology": "how you combined the outputs",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs"]
}`;try{await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83D\uDD04 Analyzing specialist contributions...",progress:.3});let e=await this.callModerator(s);await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83C\uDFA8 Weaving outputs together...",progress:.7});let t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"✨ Finalizing synthesized response...",progress:1}),t}catch(t){return{combinedOutput:e.map(e=>`**${e.roleId} Contribution:**
${e.output}`).join("\n\n"),methodology:"Simple concatenation due to synthesis error",qualityScore:.6,conflictsResolved:[],improvements:[]}}}generateLiveCommentary(e,t){let s={orchestration_started:["\uD83C\uDFAC Alright team, we've got an interesting challenge ahead!","\uD83D\uDE80 Let's break this down and see who's best suited for each part.","\uD83C\uDFAF Time to coordinate our AI specialists for optimal results."],task_decomposed:["\uD83D\uDCCB Task analysis complete! I've identified the perfect team composition.","\uD83C\uDFAA Perfect breakdown! Each specialist will handle their area of expertise.","⚡ Task decomposition successful! Ready to assign specialists."],step_assigned:[`📋 Assigning ${t.roleId} specialist to handle this part.`,`🎪 Our ${t.roleId} expert is stepping up to the plate!`,`⚡ Perfect match - ${t.roleId} is exactly what we need here.`],step_started:[`🎬 ${t.roleId} is diving deep into this challenge...`,`⚡ Watch ${t.roleId} work their specialized magic!`,`🎯 ${t.roleId} is laser-focused on delivering excellence.`],step_progress:[`🔥 ${t.roleId} is making great progress...`,`⚙️ The gears are turning smoothly in ${t.roleId}'s domain!`,`🌟 ${t.roleId} is crafting something special...`],step_streaming:[`📡 ${t.roleId} is streaming their thoughts in real-time...`,`⚡ Live updates from ${t.roleId} - watch the magic happen!`,`🌊 ${t.roleId} is flowing with brilliant insights...`],step_completed:[`✅ Excellent work from ${t.roleId}! Moving to the next phase.`,`🎉 ${t.roleId} delivered exactly what we needed. Handoff time!`,`💫 Beautiful execution by ${t.roleId}. The team is flowing perfectly.`],step_failed:[`⚠️ ${t.roleId} hit a snag, but we're adapting quickly!`,`🔄 Minor setback with ${t.roleId} - implementing recovery strategy.`,`🛠️ ${t.roleId} needs a different approach. Adjusting tactics...`],synthesis_started:["\uD83E\uDDE9 Time for the grand finale! I'm combining all these brilliant contributions...","\uD83C\uDFAD Watch as I orchestrate these individual masterpieces into one cohesive symphony!","\uD83C\uDF1F The magic happens now - weaving together all our specialists' expertise!"],synthesis_progress:["\uD83D\uDD04 Synthesis in progress - combining specialist outputs...","\uD83C\uDFA8 Weaving together the brilliant contributions...","⚙️ Processing and harmonizing all the expert insights..."],synthesis_streaming:["\uD83D\uDCE1 Streaming the synthesis process live...","\uD83C\uDF0A Watch the final result take shape in real-time...","⚡ Live synthesis - see how all pieces come together..."],synthesis_complete:["\uD83C\uDF8A Synthesis complete! All specialist outputs have been perfectly combined.","✨ The final masterpiece is ready! What an incredible team effort.","\uD83C\uDFC6 Synthesis successful! The AI team has delivered excellence."],orchestration_completed:["\uD83C\uDF89 What a performance! Our AI team delivered something truly remarkable.","✨ Mission accomplished! Each specialist played their part perfectly.","\uD83C\uDFC6 Outstanding collaboration - this is what AI teamwork looks like!"],orchestration_failed:["⚠️ The orchestration encountered issues, but we're learning from this.","\uD83D\uDD04 Orchestration failed - analyzing what went wrong for next time.","\uD83D\uDEE0️ Technical difficulties occurred - implementing improvements."],moderator_commentary:["\uD83C\uDF99️ Moderator providing guidance and coordination...","\uD83D\uDCCB Quality check and process optimization in progress...","\uD83C\uDFAF Ensuring optimal team coordination and output quality..."],specialist_message:[`💬 ${t.roleId} is sharing insights with the team...`,`🗣️ ${t.roleId} has something important to communicate...`,`📢 ${t.roleId} is contributing to the team discussion...`],moderator_assignment:[`🎯 Moderator assigning ${t.roleId} to the next task...`,`📋 Task delegation: ${t.roleId} is now taking the lead...`,`⚡ ${t.roleId} has been selected for this specialized work...`],specialist_acknowledgment:[`✅ ${t.roleId} acknowledges the assignment and is ready to proceed.`,`👍 ${t.roleId} confirms understanding and begins work.`,`🎯 ${t.roleId} is locked and loaded for this task.`],handoff_message:[`🤝 ${t.roleId} is handing off to the next specialist...`,`📤 ${t.roleId} has completed their part - passing the baton...`,`✨ ${t.roleId} finished beautifully - next specialist incoming...`],clarification_request:[`❓ ${t.roleId} needs clarification to deliver the best results...`,`🤔 ${t.roleId} is asking for more details to optimize their output...`,`💭 ${t.roleId} wants to ensure they understand the requirements perfectly...`],clarification_response:[`💡 Clarification provided - ${t.roleId} now has what they need!`,`✅ Question answered - ${t.roleId} can proceed with confidence.`,`🎯 All clear! ${t.roleId} has the details needed for success.`]}[e]||["\uD83E\uDD16 Processing..."];return s[Math.floor(Math.random()*s.length)]}async callModerator(e){let t=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.classificationApiKey}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format."},{role:"user",content:e}],temperature:.2,max_tokens:2e3,response_format:{type:"json_object"}})});if(!t.ok)throw Error(`Moderator API error: ${t.status}`);let s=await t.json(),o=s.choices?.[0]?.message?.content;if(!o)throw Error("Empty moderator response");return o}async analyzeParallelizationOpportunities(e){let t=`Analyze these orchestration steps for parallelization opportunities:

Steps:
${e.map(e=>`${e.stepNumber}. ${e.roleId}: "${e.prompt}" (depends on: ${e.dependencies.join(", ")||"none"})`).join("\n")}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;try{let e=await this.callModerator(t);return JSON.parse(e)}catch(t){return{parallelGroups:e.map(e=>[e.stepNumber]),reasoning:"Sequential execution due to analysis error",estimatedSpeedup:1}}}}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53810:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var o={};s.r(o),s.d(o,{GET:()=>h});var r=s(96559),i=s(48088),n=s(37719),a=s(32190),l=s(2507),c=s(68811),p=s(5649),d=s(55511),u=s.n(d);async function h(e,{params:t}){let{executionId:s}=await t;if(!s)return a.NextResponse.json({error:"Execution ID is required"},{status:400});let o=await (0,l.createSupabaseServerClientOnRequest)();try{let{data:e,error:t}=await o.from("orchestration_executions").select("*").eq("id",s).single();if(t||!e)return a.NextResponse.json({error:"Orchestration execution not found"},{status:404});let{data:r,error:i}=await o.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",s).eq("status","completed").order("step_number",{ascending:!0});if(i||!r||0===r.length)return a.NextResponse.json({error:"No completed steps found for synthesis"},{status:400});let n=r[0]?.prompt?.split('"')[1]||"user request",l=`You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${n}"

Here are the outputs from each specialist:

${r.map(e=>`**${e.role_id.toUpperCase()} (Step ${e.step_number}):**
${e.response}

`).join("\n")}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`,d=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!d)return a.NextResponse.json({error:"Classification API key not found"},{status:500});let h=new p.y(d,s),m=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:l}],stream:!1,temperature:.3,max_tokens:8e3})});if(!m.ok){let e=await m.text().catch(()=>"Could not read error response");return a.NextResponse.json({error:`Synthesis API call failed: ${m.status}, ${e}`},{status:m.status})}let g=await m.json(),f=g.choices?.[0]?.message?.content||g.choices?.[0]?.text||"Synthesis completed but no content was returned.";await o.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:f}).eq("id",s);let y=e?.created_at?Date.now()-new Date(e.created_at).getTime():0;return(0,c.tl)(s,{id:u().randomUUID(),execution_id:s,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:h.generateLiveCommentary("orchestration_completed",{totalSteps:r.length}),finalResult:f,totalSteps:r.length,totalDuration:y}}),a.NextResponse.json({result:f})}catch(e){return a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-fallback/[executionId]/route",pathname:"/api/orchestration/synthesis-fallback/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-fallback/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-fallback\\[executionId]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:y}=m;function w(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[4447,580,9398,3410,367],()=>s(53810));module.exports=o})();