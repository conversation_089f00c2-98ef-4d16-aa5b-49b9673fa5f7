(()=>{var e={};e.id=2580,e.ids=[2580],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78284:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var r={};s.r(r),s.d(r,{GET:()=>d});var a=s(96559),o=s(48088),i=s(37719),n=s(32190),u=s(39398);async function d(e){try{let e=(0,u.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),t=new Date;t.setDate(t.getDate()-1);let[s,r,a]=await Promise.allSettled([e.from("custom_api_configs").select("id").limit(1),e.from("api_keys").select("id").eq("status","active").limit(1),e.from("request_logs").select("id").gte("request_timestamp",t.toISOString()).limit(1)]),o=[],i="operational",d="";"rejected"===s.status?(i="down",d=s.reason?.message||"Connection failed"):s.value.error&&(i="down",d=s.value.error.message),o.push({name:"API Gateway",status:i,details:d,lastChecked:new Date().toISOString()});let p="operational",l="";"rejected"===r.status?(p="down",l=r.reason?.message||"Connection failed"):r.value.error?(p="degraded",l="Error checking active keys"):r.value.data&&0!==r.value.data.length||(p="degraded",l="No active API keys found"),o.push({name:"Routing Engine",status:p,details:l,lastChecked:new Date().toISOString()});let c="operational",g="";"rejected"===a.status?(c="down",g=a.reason?.message||"Connection failed"):a.value.error?(c="degraded",g="Error checking recent logs"):a.value.data&&0!==a.value.data.length||(c="degraded",g="No recent activity logged"),o.push({name:"Analytics",status:c,details:g,lastChecked:new Date().toISOString()});let m=o.some(e=>"down"===e.status),h=o.some(e=>"degraded"===e.status),x="operational";m?x="down":h&&(x="degraded");let v=n.NextResponse.json({overall_status:x,checks:o,last_updated:new Date().toISOString()});return v.headers.set("Cache-Control","public, max-age=30, stale-while-revalidate=60"),v.headers.set("X-Content-Type-Options","nosniff"),v}catch(e){return n.NextResponse.json({overall_status:"down",checks:[{name:"System Check",status:"down",details:"Failed to perform system health checks",lastChecked:new Date().toISOString()}],last_updated:new Date().toISOString(),error:e.message},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/system-status/route",pathname:"/api/system-status",filename:"route",bundlePath:"app/api/system-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\system-status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:g}=p;function m(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398],()=>s(78284));module.exports=r})();