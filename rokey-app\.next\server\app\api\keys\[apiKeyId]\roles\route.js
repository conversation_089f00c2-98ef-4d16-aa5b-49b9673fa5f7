(()=>{var e={};e.id=1529,e.ids=[1489,1529],e.modules={507:(e,t,i)=>{"use strict";i.d(t,{Dc:()=>s,p2:()=>r});let r=[{id:"general_chat",name:"<PERSON> Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],s=e=>r.find(t=>t.id===e)},2507:(e,t,i)=>{"use strict";i.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>n});var r=i(34386),s=i(44999);async function n(){let e=await (0,s.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,i,r){try{e.set({name:t,value:i,...r})}catch(e){}},remove(t,i){try{e.set({name:t,value:"",...i})}catch(e){}}}})}function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,i){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49722:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var r={};i.r(r),i.d(r,{GET:()=>u,POST:()=>p});var s=i(96559),n=i(48088),a=i(37719),o=i(32190),c=i(2507),d=i(507);async function u(e,{params:t}){let i=(0,c.Q)(e),{apiKeyId:r}=await t;if(!r)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{data:e,error:t}=await i.from("api_key_role_assignments").select("role_name, created_at").eq("api_key_id",r);if(t)return o.NextResponse.json({error:"Failed to fetch role assignments",details:t.message},{status:500});let s=e.map(e=>{let t=(0,d.Dc)(e.role_name);return{...e,role_details:t||{id:e.role_name,name:e.role_name,description:"Custom role (details managed globally)"}}});return o.NextResponse.json(s||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e,{params:t}){let i=(0,c.Q)(e),{apiKeyId:r}=await t,{data:{session:s},error:n}=await i.auth.getSession();if(n||!s?.user)return o.NextResponse.json({error:"Unauthorized: You must be logged in to assign roles."},{status:401});let a=s.user.id;if(!r)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{role_name:t}=await e.json();if(!t||"string"!=typeof t)return o.NextResponse.json({error:"Role name (role_id) is required and must be a string"},{status:400});let{data:s,error:n}=await i.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",r).single();if(n||!s)return o.NextResponse.json({error:"API Key not found or failed to fetch its details"},{status:404});let c=s.custom_api_configs?.user_id;if(!c)return o.NextResponse.json({error:"Could not determine the config owner for the API Key."},{status:500});if(c&&a!==c)return o.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let u=d.p2.some(e=>e.id===t),p=!1;if(!u){let{data:e,error:r}=await i.from("user_custom_roles").select("id").eq("user_id",a).eq("role_id",t).maybeSingle();if(r)return o.NextResponse.json({error:"Error validating role.",details:r.message},{status:500});e&&(p=!0)}if(!u&&!p)return o.NextResponse.json({error:`Invalid role_name: ${t}. Not a predefined role or a custom role you own.`},{status:400});let{custom_api_config_id:l}=s,{data:g,error:m}=await i.from("api_key_role_assignments").insert({api_key_id:r,custom_api_config_id:l,role_name:t}).select().single();if(m){if("23505"===m.code){if(m.message.includes("unique_api_key_role"))return o.NextResponse.json({error:"This API key already has this role assigned.",details:m.message},{status:409});if(m.message.includes("unique_role_per_custom_config"))return o.NextResponse.json({error:"This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.",details:m.message},{status:409});return o.NextResponse.json({error:"Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.",details:m.message,code:m.code},{status:409})}return o.NextResponse.json({error:"Failed to assign role to API key",details:m.message},{status:500})}return o.NextResponse.json(g,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/route",pathname:"/api/keys/[apiKeyId]/roles",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=l;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>i(49722));module.exports=r})();