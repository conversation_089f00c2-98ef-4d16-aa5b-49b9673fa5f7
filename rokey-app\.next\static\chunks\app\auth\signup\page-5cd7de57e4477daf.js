(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{11387:(e,a,s)=>{Promise.resolve().then(s.bind(s,11730))},11730:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>g});var t=s(95155),r=s(12115),i=s(6874),l=s.n(i),n=s(66766),o=s(55020),c=s(29337),d=s(10184),m=s(48987),x=s(21394),u=s(52643),h=s(35695);function f(){let[e,a]=(0,r.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),[s,i]=(0,r.useState)(!1),[f,g]=(0,r.useState)(!1),[p,b]=(0,r.useState)(!1),[y,N]=(0,r.useState)(""),[w,j]=(0,r.useState)(!1),v=(0,h.useRouter)(),k=(0,h.useSearchParams)(),C=(0,u.u)(),D=k.get("plan");(0,r.useEffect)(()=>{if(!D||!["starter","professional","enterprise"].includes(D))return void v.push("/pricing")},[v,D,C]);let P=e=>{a(a=>({...a,[e.target.name]:e.target.value}))},S=async a=>{if(a.preventDefault(),b(!0),N(""),e.password!==e.confirmPassword){N("Passwords do not match"),b(!1);return}if(e.password.length<8){N("Password must be at least 8 characters long"),b(!1);return}if(!w){N("Please agree to the Terms of Service and Privacy Policy"),b(!1);return}try{let{data:a,error:t}=await C.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.firstName,last_name:e.lastName,full_name:"".concat(e.firstName," ").concat(e.lastName),plan:D||"professional",payment_status:"pending"}}});if(t){if(t.message.includes("already registered"))try{var s;let{data:a,error:t}=await C.auth.signInWithPassword({email:e.email,password:e.password});if(a.user&&(null==(s=a.user.user_metadata)?void 0:s.payment_status)==="pending"){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"PENDING_USER_RETRY",userId:a.user.id,email:e.email,selectedPlan:D,message:"Allowing pending user to retry checkout - user is now signed in",sessionExists:!!a.session})}).catch(()=>{});let s=null,t=0;for(;!s&&t<3;){let{data:{user:e}}=await C.auth.getUser();s=e?{user:e}:null,t++,!s&&t<3&&await new Promise(e=>setTimeout(e,1e3))}s&&s.user?v.push("/checkout?plan=".concat(D,"&user_id=").concat(s.user.id,"&email=").concat(encodeURIComponent(e.email))):(N("Failed to establish session. Please try signing in manually."),v.push("/auth/signin?plan=".concat(D,"&email=").concat(encodeURIComponent(e.email),"&message=session_failed")))}else window.location.href="/auth/signin?plan=".concat(D,"&message=account_exists")}catch(e){window.location.href="/auth/signin?plan=".concat(D,"&message=account_exists")}else N(t.message);return}if(!a.user)return void N("Failed to create account. Please try again.");await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_CREATED_PAYMENT_PENDING",userId:a.user.id,email:e.email,selectedPlan:D,redirectUrl:"/checkout?plan=".concat(D,"&user_id=").concat(a.user.id,"&email=").concat(encodeURIComponent(e.email)),paymentStatus:"pending",message:"User created successfully, user should be automatically signed in"})}).catch(()=>{});let{data:{user:r}}=await C.auth.getUser();r?setTimeout(()=>{window.location.href="/checkout?plan=".concat(D,"&user_id=").concat(r.id,"&email=").concat(encodeURIComponent(e.email))},500):window.location.href="/auth/signin?plan=".concat(D,"&checkout_user_id=").concat(a.user.id,"&email=").concat(encodeURIComponent(e.email),"&message=account_created")}catch(e){N(e.message||"Failed to process signup. Please try again.")}finally{b(!1)}},_=[{text:"At least 8 characters",met:e.password.length>=8},{text:"Contains uppercase letter",met:/[A-Z]/.test(e.password)},{text:"Contains lowercase letter",met:/[a-z]/.test(e.password)},{text:"Contains number",met:/\d/.test(e.password)}];return(0,t.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)(x.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,t.jsx)("div",{className:"absolute inset-0",children:(0,t.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,t.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsx)(o.PY1.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,t.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"30px 30px"}}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,t.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Join RouKey Today"}),(0,t.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Get started with ",(0,t.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," access to 300+ AI models"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"300+ AI Models"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,t.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]})]})]})]})}),(0,t.jsxs)(o.PY1.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)(l(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,t.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,t.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign Up"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Create your AI gateway account"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)("button",{onClick:async()=>{await C.auth.signOut(),localStorage.clear(),window.location.reload()},className:"text-xs text-gray-400 hover:text-gray-600",children:"\uD83D\uDD27 Clear Session"}),(0,t.jsx)("button",{onClick:()=>{N(""),a({firstName:"",lastName:"",email:"",password:"",confirmPassword:""})},className:"text-xs text-gray-400 hover:text-gray-600",children:"\uD83D\uDDD1️ Clear Form"})]}),D&&(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,t.jsxs)("span",{className:"text-[#ff6b35] font-semibold text-lg",children:[D.charAt(0).toUpperCase()+D.slice(1)," Plan Selected"]}),(0,t.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"})]}),(0,t.jsx)("p",{className:"text-center text-gray-600 text-sm mt-1",children:"You'll be redirected to checkout after creating your account"}),(0,t.jsx)("div",{className:"text-center mt-2",children:(0,t.jsx)(l(),{href:"/pricing",className:"text-[#ff6b35] hover:text-[#e55a2b] text-sm font-medium transition-colors",children:"Change plan"})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"20px 20px"}}),(0,t.jsxs)("div",{className:"relative z-10",children:[y&&(0,t.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:[(0,t.jsx)("p",{className:"text-red-600 text-sm",children:y}),y.includes("already registered")&&(0,t.jsx)("div",{className:"mt-3",children:(0,t.jsx)(l(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold text-sm transition-colors",children:"→ Go to Sign In page"})})]}),(0,t.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDC64 First Name"}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:e.firstName,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"John"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDC64 Last Name"}),(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:e.lastName,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Doe"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",name:"password",type:s?"text":"password",required:!0,value:e.password,onChange:P,className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Create a strong password"}),(0,t.jsx)("button",{type:"button",onClick:()=>i(!s),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:s?(0,t.jsx)(m.A,{className:"h-5 w-5"}):(0,t.jsx)(d.A,{className:"h-5 w-5"})})]}),e.password&&(0,t.jsx)("div",{className:"mt-3 p-4 bg-gray-50 rounded-xl space-y-2",children:_.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-3 ".concat(e.met?"text-green-500":"text-gray-300")}),(0,t.jsx)("span",{className:e.met?"text-green-600 font-medium":"text-gray-500",children:e.text})]},a))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:f?"text":"password",required:!0,value:e.confirmPassword,onChange:P,className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Confirm your password"}),(0,t.jsx)("button",{type:"button",onClick:()=>g(!f),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:f?(0,t.jsx)(m.A,{className:"h-5 w-5"}):(0,t.jsx)(d.A,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl",children:[(0,t.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:w,onChange:e=>j(e.target.checked),className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg mt-1"}),(0,t.jsxs)("label",{htmlFor:"terms",className:"ml-3 block text-sm text-gray-700",children:["I agree to the"," ",(0,t.jsx)(l(),{href:"/terms",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Terms of Service"})," ","and"," ",(0,t.jsx)(l(),{href:"/privacy",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Privacy Policy"})]})]}),(0,t.jsx)("button",{type:"submit",disabled:p,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:p?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating account..."]}):"Create Account"})]})]})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsxs)("p",{className:"text-gray-600 text-lg",children:["Already have an account?"," ",(0,t.jsx)(l(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign in"})]})})]})]})})]})}function g(){return(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,t.jsx)(f,{})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7871,2115,8888,1459,5738,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(11387)),_N_E=e.O()}]);