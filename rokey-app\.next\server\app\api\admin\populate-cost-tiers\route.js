(()=>{var e={};e.id=9426,e.ids=[1489,9426],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>o});var s=r(34386),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>p});var i=r(96559),o=r(48088),n=r(37719),a=r(32190),c=r(2507);async function p(e){try{let t=(0,c.Q)(e),{data:r,error:s}=await t.from("models").select("id, name, display_name, provider, input_token_price, output_token_price");if(s)return a.NextResponse.json({error:"Failed to fetch models"},{status:500});if(!r||0===r.length)return a.NextResponse.json({error:"No models found"},{status:404});let i=r.map(e=>{let t,r,s;if(!e.input_token_price||!e.output_token_price)return null;let i=500*e.input_token_price+500*e.output_token_price;i<=.001?(t="cheap",r=6.5,s=["chat","simple_questions","basic_writing"]):i<=.01?(t="moderate",r=7.5,s=["analysis","writing","explanations","moderate_coding"]):(t="premium",r=8.5,s=["complex_reasoning","advanced_coding","research","creative_writing"]);let o=r/i;return{model_id:e.id,provider:e.provider,cost_tier:t,avg_cost_per_1k_tokens:i,typical_quality_score:r,best_for_tasks:s,cost_efficiency_score:o}}).filter(Boolean),{data:o,error:n}=await t.from("model_cost_tiers").upsert(i,{onConflict:"model_id,provider",ignoreDuplicates:!1}).select();if(n)return a.NextResponse.json({error:"Failed to insert cost tiers"},{status:500});return a.NextResponse.json({success:!0,message:`Successfully populated ${o?.length||0} model cost tiers`,tiers:o})}catch(e){return a.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e){try{let t=(0,c.Q)(e),{data:r,error:s}=await t.from("model_cost_tiers").select("*").order("cost_tier",{ascending:!0}).order("cost_efficiency_score",{ascending:!1});if(s)return a.NextResponse.json({error:"Failed to fetch cost tiers"},{status:500});let i={cheap:r?.filter(e=>"cheap"===e.cost_tier)||[],moderate:r?.filter(e=>"moderate"===e.cost_tier)||[],premium:r?.filter(e=>"premium"===e.cost_tier)||[]};return a.NextResponse.json({success:!0,totalTiers:r?.length||0,tiers:i,allTiers:r})}catch(e){return a.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/populate-cost-tiers/route",pathname:"/api/admin/populate-cost-tiers",filename:"route",bundlePath:"app/api/admin/populate-cost-tiers/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\admin\\populate-cost-tiers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:_}=l;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(5162));module.exports=s})();