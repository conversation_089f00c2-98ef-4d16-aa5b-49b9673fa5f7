"use strict";exports.id=9704,exports.ids=[9704],exports.modules={99704:(e,t,o)=>{function T(e,t,o){let T=e.getReader(),r=new TextDecoder;return new TextEncoder,new ReadableStream({async start(e){let t=!1;Date.now();try{for(;;){let{done:o,value:n}=await T.read();if(o){e.close();break}let a=r.decode(n,{stream:!0});if(!t&&a.includes("delta"))try{for(let e of a.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let o=e.substring(6);try{let e=JSON.parse(o);if(e.choices?.[0]?.delta?.content){Date.now(),t=!0;break}}catch(e){}}}catch(e){t||(Date.now(),t=!0)}e.enqueue(n)}}catch(t){e.error(t)}}})}function r(e,t,o){void 0!==o.timeToFirstToken&&(o.timeToFirstToken<500||o.timeToFirstToken<1e3||o.timeToFirstToken),o.totalStreamTime,o.totalTokens,o.averageTokenLatency}function n(e,t){return{provider:e||"unknown",model:t||"unknown"}}function a(e){return Math.ceil(e.length/4)}o.r(t),o.d(t,{PERFORMANCE_THRESHOLDS:()=>i,createFirstTokenTrackingStream:()=>T,estimateTokenCount:()=>a,evaluatePerformance:()=>l,getProviderModelFromContext:()=>n,logStreamingPerformance:()=>r});let i={EXCELLENT_FIRST_TOKEN:500,GOOD_FIRST_TOKEN:1e3,SLOW_FIRST_TOKEN:2e3,EXCELLENT_TOTAL:3e3,GOOD_TOTAL:5e3,SLOW_TOTAL:1e4,TARGET_TOKEN_LATENCY:50};function l(e){let t=e.timeToFirstToken?e.timeToFirstToken<i.EXCELLENT_FIRST_TOKEN?"excellent":e.timeToFirstToken<i.GOOD_FIRST_TOKEN?"good":e.timeToFirstToken<i.SLOW_FIRST_TOKEN?"slow":"very_slow":"very_slow",o=e.totalStreamTime?e.totalStreamTime<i.EXCELLENT_TOTAL?"excellent":e.totalStreamTime<i.GOOD_TOTAL?"good":e.totalStreamTime<i.SLOW_TOTAL?"slow":"very_slow":"very_slow",T=e.averageTokenLatency?e.averageTokenLatency<i.TARGET_TOKEN_LATENCY?"excellent":e.averageTokenLatency<2*i.TARGET_TOKEN_LATENCY?"good":e.averageTokenLatency<4*i.TARGET_TOKEN_LATENCY?"slow":"very_slow":"very_slow",r=["excellent","good","slow","very_slow"],n=[t,o,T].reduce((e,t)=>r.indexOf(t)>r.indexOf(e)?t:e,"excellent");return{firstTokenGrade:t,totalTimeGrade:o,tokenLatencyGrade:T,overallGrade:n}}}};