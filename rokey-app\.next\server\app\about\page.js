(()=>{var e={};e.id=7220,e.ids=[7220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5152:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),i=r(52535),a=r(85814),o=r.n(a),l=r(62392),n=r(50942),d=r(19682),c=r(93635),x=r(57093),u=r(17457);let m=[{label:"AI Models Supported",value:"300+"},{label:"API Requests Processed",value:"10M+"},{label:"Developers Trust Us",value:"5,000+"},{label:"Uptime Guarantee",value:"99.9%"}],p=[{icon:l.A,title:"Performance First",description:"We obsess over speed, reliability, and efficiency in everything we build."},{icon:n.A,title:"Security by Design",description:"Enterprise-grade security isn&apos;t an afterthought—it&apos;s built into our foundation."},{icon:d.A,title:"Innovation Driven",description:"We&apos;re constantly pushing the boundaries of what&apos;s possible with AI routing."},{icon:c.A,title:"Solo Built",description:"Built entirely by one developer who faced these exact problems and solved them."}],h={name:"Okoro David Chukwunyerem",role:"Founder & Developer"};function b(){return(0,s.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,s.jsx)(x.A,{}),(0,s.jsxs)("main",{className:"pt-20",children:[(0,s.jsx)("section",{className:"py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["From $500 API Bills to",(0,s.jsx)("span",{className:"text-[#ff6b35] block",children:"Unlimited AI Access"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mb-8",children:"The inspiring story of how one developer's frustration with expensive AI routing led to building RouKey - the world's most intelligent AI gateway with unlimited requests to 300+ models, completely solo."})]})})}),(0,s.jsx)("section",{className:"py-16",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:m.map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2",children:e.value}),(0,s.jsx)("div",{className:"text-gray-600 font-medium",children:e.label})]},e.label))})})}),(0,s.jsx)("section",{className:"py-20 bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"The Breaking Point"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Every developer has that moment when they decide to build the tool they wish existed. This is mine."})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},className:"prose prose-lg mx-auto text-gray-700",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"It was 2 AM."}),' I was deep into developing a game that heavily relied on AI for procedural content generation. My API bills were skyrocketing, I\'d hit rate limits on three different providers, and the "intelligent" routing tool I was paying for had just routed my simple chat request to GPT-4 Turbo for the hundredth time that day.']}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"That's when it hit me."})," I'm Okoro David Chukwunyerem, and I've been building games and apps for years. I know how to solve complex routing problems. Why was I trusting someone else's broken solution when I could build something that actually works?"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"The eureka moment:"})," While experimenting with different providers, I discovered that by intelligently routing between multiple free trial API keys, I could get essentially unlimited usage for testing. No more $500 monthly bills for development work. No more hitting rate limits mid-sprint."]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Six months later,"})," what started as a weekend hack to solve my own problems had evolved into RouKey - a platform that gives any developer unlimited access to 300+ AI models with truly intelligent routing. Built entirely solo, tested in the trenches of real development work."]}),(0,s.jsx)("p",{className:"text-[#ff6b35] font-semibold text-lg border-l-4 border-[#ff6b35] pl-6 italic",children:'"I built RouKey because I was tired of choosing between going broke or building slowly. Now thousands of developers can test fearlessly and build faster than ever before."'})]})]})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"My Values"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"These principles guide how I built RouKey and how I continue to develop it."})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:p.map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center",children:(0,s.jsx)(e.icon,{className:"w-6 h-6 text-white"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600",children:e.description})]})]},e.title))})]})}),(0,s.jsxs)("section",{className:"py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,s.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:`
                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                `,backgroundSize:"60px 60px"}})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-20",children:[(0,s.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Meet the",(0,s.jsx)("span",{className:"text-[#ff6b35] block",children:"Solo Founder"})]}),(0,s.jsx)("p",{className:"text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed",children:"One developer's journey from frustration to building the ultimate AI routing platform"})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},className:"bg-white rounded-3xl shadow-2xl overflow-hidden max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-0",children:[(0,s.jsx)("div",{className:"relative bg-gradient-to-br from-[#ff6b35] to-[#f7931e] p-8 flex items-center justify-center min-h-[700px] group",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-[500px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-4 border-white/20 group-hover:scale-105 transition-transform duration-500",children:(0,s.jsx)("img",{src:"/founder.jpg",alt:"Okoro David Chukwunyerem - Founder and Solo Developer of RouKey AI Gateway",className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700",loading:"lazy"})}),(0,s.jsx)("div",{className:"absolute -top-6 -left-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-12 hover:rotate-0 transition-transform duration-300",children:(0,s.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"Solo Built"})}),(0,s.jsx)("div",{className:"absolute -bottom-6 -right-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-12 hover:rotate-0 transition-transform duration-300",children:(0,s.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"300+ Models"})}),(0,s.jsx)("div",{className:"absolute top-1/2 -right-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-6 hover:rotate-0 transition-transform duration-300",children:(0,s.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"Game Dev"})}),(0,s.jsx)("div",{className:"absolute bottom-1/4 -left-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-6 hover:rotate-0 transition-transform duration-300",children:(0,s.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"∞ Requests"})})]})}),(0,s.jsxs)("div",{className:"p-12 flex flex-col justify-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-4xl font-bold text-gray-900 mb-3",children:h.name}),(0,s.jsx)("p",{className:"text-2xl text-[#ff6b35] font-semibold mb-6",children:h.role}),(0,s.jsx)("div",{className:"w-20 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full mb-8"})]}),(0,s.jsxs)("div",{className:"space-y-6 text-lg text-gray-700 leading-relaxed",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{className:"text-gray-900",children:"Game developer turned AI infrastructure pioneer."}),"After countless nights wrestling with broken routing tools and mounting API bills, I decided to build the solution I wished existed."]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{className:"text-gray-900",children:"The breakthrough:"})," While developing games that heavily used AI, I discovered that intelligent routing between multiple free trial API keys could give me essentially unlimited testing access. No more $500 monthly bills for development work."]}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-2xl p-6 border-l-4 border-[#ff6b35]",children:(0,s.jsx)("p",{className:"text-[#ff6b35] font-semibold italic",children:'"I built RouKey because I was tired of choosing between going broke or building slowly. Six months later, thousands of developers are testing fearlessly and building faster than ever."'})}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{className:"text-gray-900",children:"Every line of code"})," in RouKey was written by me. Every feature was born from a real problem I faced. That's why it actually works."]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-6 mt-10 pt-8 border-t border-gray-200",children:[(0,s.jsxs)("div",{className:"text-center group",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"1"}),(0,s.jsx)("div",{className:"text-gray-600 text-sm",children:"Solo Developer"})]}),(0,s.jsxs)("div",{className:"text-center group",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"300+"}),(0,s.jsx)("div",{className:"text-gray-600 text-sm",children:"AI Models"})]}),(0,s.jsxs)("div",{className:"text-center group",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"∞"}),(0,s.jsx)("div",{className:"text-gray-600 text-sm",children:"API Requests"})]})]})]})]})})]})]}),(0,s.jsxs)("section",{className:"py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0",children:[(0,s.jsx)("div",{className:"absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"}),(0,s.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse",style:{animationDelay:"2s"}})]}),(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,s.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold text-white mb-8",children:["Ready to Build",(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block",children:"Without Limits?"})]}),(0,s.jsx)("p",{className:"text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed",children:"Join thousands of developers who've discovered the secret to unlimited AI testing. Built by a developer who faced your exact problems."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center mb-16",children:[(0,s.jsx)(o(),{href:"/auth/signup",prefetch:!0,children:(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer",children:"Start Building Now"})}),(0,s.jsx)(o(),{href:"/pricing",prefetch:!0,children:(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white/10 backdrop-blur-sm text-white px-12 py-5 rounded-2xl font-bold text-xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer",children:"View Pricing"})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"∞"}),(0,s.jsx)("div",{className:"text-gray-400",children:"API Requests"}),(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"No limits, ever"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"300+"}),(0,s.jsx)("div",{className:"text-gray-400",children:"AI Models"}),(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"All providers"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"$0"}),(0,s.jsx)("div",{className:"text-gray-400",children:"Overage Fees"}),(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Pay only your API costs"})]})]})]})})]})]}),(0,s.jsx)(u.A,{})]})}},10782:(e,t,r)=>{Promise.resolve().then(r.bind(r,5152))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15510:(e,t,r)=>{Promise.resolve().then(r.bind(r,28770))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19682:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(43210);let i=s.forwardRef(function({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))})},27910:e=>{"use strict";e.exports=require("stream")},28770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30423:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28770)),"C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,40989)),"C:\\RoKey App\\rokey-app\\src\\app\\about\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40989:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});let s={title:"About RouKey - The Solo-Built AI Gateway Revolution | RouKey",description:"Meet Okoro David Chukwunyerem, the solo developer who built RouKey from frustration with expensive AI routing. Discover how one developer's late-night coding sessions created the ultimate AI gateway with unlimited requests to 300+ models.",keywords:"RouKey founder, AI gateway creator, solo developer, AI routing platform, unlimited AI requests, multi-model AI, cost-effective AI solutions",openGraph:{title:"About RouKey - The Solo-Built AI Gateway Revolution",description:"From $500 monthly API bills to unlimited AI access - the inspiring story of how RouKey was built by one developer who refused to accept broken solutions.",type:"website",url:"https://roukey.online/about",images:[{url:"https://roukey.online/founder.jpg",width:1200,height:630,alt:"Okoro David Chukwunyerem - Founder of RouKey"}]},twitter:{card:"summary_large_image",title:"About RouKey - The Solo-Built AI Gateway Revolution",description:"Meet the solo developer who built RouKey to solve the frustrations of expensive AI routing and unlimited access to 300+ AI models.",images:["https://roukey.online/founder.jpg"]},alternates:{canonical:"https://roukey.online/about"}};function i({children:e}){return e}},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(43210);let i=s.forwardRef(function({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62392:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(43210);let i=s.forwardRef(function({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93635:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(43210);let i=s.forwardRef(function({title:e,titleId:t,...r},i){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7482,2535,4912,6389],()=>r(30423));module.exports=s})();