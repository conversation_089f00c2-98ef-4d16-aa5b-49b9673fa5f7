(()=>{var e={};e.id=6658,e.ids=[6658],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11961:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>i,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>p});var o=t(96559),a=t(48088),n=t(37719),u=t(32190);async function p(e){try{let{action:r,...t}=await e.json();return new Date().toISOString(),u.NextResponse.json({success:!0})}catch(e){return u.NextResponse.json({error:"Debug failed"},{status:500})}}let i=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/debug/checkout/route",pathname:"/api/debug/checkout",filename:"route",bundlePath:"app/api/debug/checkout/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\debug\\checkout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:x}=i;function l(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(11961));module.exports=s})();