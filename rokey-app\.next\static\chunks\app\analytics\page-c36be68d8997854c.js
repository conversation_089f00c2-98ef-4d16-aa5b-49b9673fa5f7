(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1745],{18959:(e,s,t)=>{Promise.resolve().then(t.bind(t,52936))},52936:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(95155),r=t(12115);let l=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var i=t(27572),n=t(58828),c=t(64274),d=t(72227),o=t(5500);let x=r.forwardRef(function(e,s){let{title:t,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},l),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"}))});var m=t(6865),h=t(58397),g=t(28960),u=t(55628),p=t(55233),j=t(48666),v=t(86474);function b(){let[e,s]=(0,r.useState)(null),[t,b]=(0,r.useState)(null),[y,N]=(0,r.useState)(null),[f,w]=(0,r.useState)([]),[_,k]=(0,r.useState)(null),[q,C]=(0,r.useState)([]),[S,A]=(0,r.useState)(!0),[P,D]=(0,r.useState)(null),[M,L]=(0,r.useState)("30"),[F,I]=(0,r.useState)(""),[O,R]=(0,r.useState)(!1),[E,B]=(0,r.useState)(""),[T,U]=(0,r.useState)(""),[W,z]=(0,r.useState)(100),[G,H]=(0,r.useState)(!1);(0,r.useEffect)(()=>{V()},[]);let V=async()=>{try{let e=await fetch("/api/custom-configs");if(e.ok){let s=await e.json();C(s)}}catch(e){}},Z=(0,r.useCallback)(async()=>{try{let e;A(!0),D(null);let t=new URLSearchParams,a=new Date;E&&T?(e=new Date(E),a=new Date(T),t.append("startDate",e.toISOString()),t.append("endDate",a.toISOString())):M&&((e=new Date).setDate(e.getDate()-parseInt(M)),t.append("startDate",e.toISOString())),F&&t.append("customApiConfigId",F);let r=new URLSearchParams,l=a.getTime()-e.getTime(),i=new Date(e.getTime()-l),n=new Date(e.getTime());r.append("startDate",i.toISOString()),r.append("endDate",n.toISOString()),F&&r.append("customApiConfigId",F);let[c,d,o,x]=await Promise.all([fetch("/api/analytics/summary?".concat(t.toString(),"&groupBy=provider")),fetch("/api/analytics/summary?".concat(t.toString(),"&groupBy=model")),fetch("/api/analytics/summary?".concat(t.toString(),"&groupBy=day")),fetch("/api/analytics/summary?".concat(r.toString(),"&groupBy=day"))]);if(!c.ok||!d.ok||!o.ok)throw Error("Failed to fetch analytics data");let m=await c.json(),h=await d.json(),g=await o.json(),u=x.ok?await x.json():null;s(m),b(h),k(u);let p=g.grouped_data.map(e=>({period:e.period,cost:e.cost,requests:e.requests,tokens:e.input_tokens+e.output_tokens}));w(p)}catch(e){D(e.message)}finally{A(!1)}},[M,F,E,T]);(0,r.useEffect)(()=>{Z()},[Z,M,F,E,T]);let Y=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),$=e=>new Intl.NumberFormat("en-US").format(e),J=(e,s)=>{if(0===s)return{percentage:0,isPositive:!0};let t=(e-s)/s*100;return{percentage:Math.abs(t),isPositive:t>=0}};if(S)return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},s))})]});if(P)return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-6",children:"\uD83D\uDCCA Advanced Analytics"}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsxs)("p",{className:"text-red-600 mb-4",children:["Error loading analytics: ",P]}),(0,a.jsx)("button",{onClick:Z,className:"btn-primary",children:"Retry"})]})]});let K=null==e?void 0:e.summary;K&&(K.total_cost,K.successful_requests);let Q=K?K.total_cost/parseInt(M)*30:0,X=(()=>{let s=[],t=null==e?void 0:e.summary;if(!t)return s;let a=t.total_cost/parseInt(M)*30;return a>.8*W&&s.push({type:a>W?"danger":"warning",title:a>W?"Budget Exceeded":"Budget Warning",message:"Projected monthly cost: ".concat(Y(a)," (Budget: ").concat(Y(W),")"),value:"".concat((a/W*100).toFixed(0),"%")}),t.success_rate<95&&s.push({type:t.success_rate<90?"danger":"warning",title:"Low Success Rate",message:"Current success rate is ".concat(t.success_rate.toFixed(1),"%. Consider reviewing failed requests."),value:"".concat(t.success_rate.toFixed(1),"%")}),t.average_cost_per_request>.01&&s.push({type:"warning",title:"High Cost Per Request",message:"Average cost per request is ".concat(Y(t.average_cost_per_request),". Consider optimizing model usage."),value:Y(t.average_cost_per_request)}),s})(),ee=(()=>{let s=[],a=null==e?void 0:e.summary;null==e||e.grouped_data;let r=(null==t?void 0:t.grouped_data)||[];if(!a)return s;let l=r.sort((e,s)=>s.cost-e.cost)[0];if(l&&l.cost>.3*a.total_cost){let e=.2*l.cost;s.push({type:"cost_optimization",title:"Optimize Expensive Model Usage",description:"".concat(l.name," accounts for ").concat((l.cost/a.total_cost*100).toFixed(1),"% of your costs. Consider using a more cost-effective alternative for simpler tasks."),potential_savings:e,icon:g.A})}a.success_rate<98&&s.push({type:"performance",title:"Improve Request Reliability",description:"Your success rate is ".concat(a.success_rate.toFixed(1),"%. Implement retry logic and error handling to improve reliability."),icon:m.A});let i=(a.total_input_tokens+a.total_output_tokens)/a.total_requests;return i>1e3&&s.push({type:"efficiency",title:"Optimize Token Usage",description:"Average ".concat($(i)," tokens per request. Consider breaking down large prompts or using more efficient models."),icon:h.A}),s})(),es=null==_?void 0:_.summary,et=es?J((null==K?void 0:K.total_cost)||0,es.total_cost):null,ea=es?J((null==K?void 0:K.total_requests)||0,es.total_requests):null;return(0,a.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Analytics Overview"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your LLM usage, costs, and performance across all providers"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("select",{value:M,onChange:e=>L(e.target.value),className:"px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,a.jsx)("option",{value:"7",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90",children:"Last 90 days"})]}),(0,a.jsxs)("button",{onClick:()=>R(!O),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2 inline"}),"Filters"]})]})]}),O&&(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Advanced Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Range"}),(0,a.jsxs)("select",{value:M,onChange:e=>L(e.target.value),className:"input-field",children:[(0,a.jsx)("option",{value:"7",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90",children:"Last 90 days"}),(0,a.jsx)("option",{value:"365",children:"Last year"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Configuration"}),(0,a.jsxs)("select",{value:F,onChange:e=>I(e.target.value),className:"input-field",children:[(0,a.jsx)("option",{value:"",children:"All Configurations"}),q.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Date"}),(0,a.jsx)("input",{type:"date",value:E,onChange:e=>B(e.target.value),className:"input-field"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"End Date"}),(0,a.jsx)("input",{type:"date",value:T,onChange:e=>U(e.target.value),className:"input-field"})]})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,a.jsx)("button",{onClick:Z,className:"btn-primary",children:"Apply Filters"}),(0,a.jsx)("button",{onClick:()=>{L("30"),I(""),B(""),U("")},className:"btn-secondary",children:"Reset Filters"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Usage Analytics"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Cost trends over time"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Cost"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-cyan-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:"Requests"})]})]})]}),(0,a.jsx)(e=>{let{data:s}=e;if(!s.length)return(0,a.jsx)("div",{className:"h-64 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No data available"})]})});let t=Math.max(...s.map(e=>e.cost)),r=Math.min(...s.map(e=>e.cost)),l=Math.max(...s.map(e=>e.requests)),i=Math.min(...s.map(e=>e.requests)),n=t-r||1,c=l-i||1;return(0,a.jsx)("div",{className:"relative h-64 w-full",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 600 200",children:[(0,a.jsxs)("defs",{children:[(0,a.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0.05"})]}),(0,a.jsxs)("linearGradient",{id:"requestGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#06b6d4",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#06b6d4",stopOpacity:"0.05"})]}),(0,a.jsx)("pattern",{id:"grid",width:"40",height:"40",patternUnits:"userSpaceOnUse",children:(0,a.jsx)("path",{d:"M 40 0 L 0 0 0 40",fill:"none",stroke:"#f1f5f9",strokeWidth:"0.5"})})]}),(0,a.jsx)("rect",{width:"600",height:"200",fill:"url(#grid)"}),(0,a.jsx)("polygon",{fill:"url(#costGradient)",points:"0,200 ".concat(s.map((e,t)=>{let a=t/Math.max(s.length-1,1)*600,l=200-(e.cost-r)/n*160;return"".concat(a,",").concat(l)}).join(" ")," 600,200")}),(0,a.jsx)("polygon",{fill:"url(#requestGradient)",points:"0,200 ".concat(s.map((e,t)=>{let a=t/Math.max(s.length-1,1)*600,r=200-(e.requests-i)/c*160;return"".concat(a,",").concat(r)}).join(" ")," 600,200")}),(0,a.jsx)("polyline",{fill:"none",stroke:"#8b5cf6",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",points:s.map((e,t)=>{let a=t/Math.max(s.length-1,1)*600,l=200-(e.cost-r)/n*160;return"".concat(a,",").concat(l)}).join(" ")}),(0,a.jsx)("polyline",{fill:"none",stroke:"#06b6d4",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",points:s.map((e,t)=>{let a=t/Math.max(s.length-1,1)*600,r=200-(e.requests-i)/c*160;return"".concat(a,",").concat(r)}).join(" ")}),s.map((e,t)=>{let l=t/Math.max(s.length-1,1)*600,d=200-(e.cost-r)/n*160,o=200-(e.requests-i)/c*160;return(0,a.jsxs)("g",{children:[(0,a.jsx)("circle",{cx:l,cy:d,r:"3",fill:"#8b5cf6",stroke:"white",strokeWidth:"2",className:"hover:r-5 transition-all duration-200 cursor-pointer",children:(0,a.jsx)("title",{children:"".concat(e.period,": ").concat(Y(e.cost))})}),(0,a.jsx)("circle",{cx:l,cy:o,r:"3",fill:"#06b6d4",stroke:"white",strokeWidth:"2",className:"hover:r-5 transition-all duration-200 cursor-pointer",children:(0,a.jsx)("title",{children:"".concat(e.period,": ").concat($(e.requests)," requests")})})]},t)})]})})},{data:f}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:Y((null==K?void 0:K.total_cost)||0)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total spend this period"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Cost Distribution"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"By provider"})]}),(null==e?void 0:e.grouped_data.length)&&K?(0,a.jsx)(e=>{let{data:s,total:t}=e;if(!s.length)return(0,a.jsx)("div",{className:"h-48 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No data available"})]})});let r=["#f43f5e","#8b5cf6","#06b6d4","#10b981","#f59e0b","#ef4444"],l=124*Math.PI,i=0;return(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsxs)("svg",{width:"160",height:"160",className:"transform -rotate-90",children:[(0,a.jsx)("circle",{cx:"80",cy:"80",r:62,stroke:"#f8fafc",strokeWidth:16,fill:"transparent"}),s.map((e,s)=>{let n=e.cost/t*100,c="".concat(n/100*l," ").concat(l),d=-(i/100*l);return i+=n,(0,a.jsx)("circle",{cx:"80",cy:"80",r:62,stroke:r[s%r.length],strokeWidth:16,strokeDasharray:c,strokeDashoffset:d,fill:"transparent",strokeLinecap:"round",className:"transition-all duration-300 hover:opacity-80 cursor-pointer",style:{filter:"drop-shadow(0 2px 4px rgba(0,0,0,0.1))"}},s)})]}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:s.length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Providers"})]})})]}),(0,a.jsx)("div",{className:"space-y-2 w-full",children:s.slice(0,4).map((e,s)=>{let l=(e.cost/t*100).toFixed(1);return(0,a.jsxs)("div",{className:"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 shadow-sm",style:{backgroundColor:r[s]}}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 capitalize",children:e.name})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-semibold text-gray-900",children:[l,"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:Y(e.cost)})]})]},s)})})]})},{data:e.grouped_data,total:K.total_cost}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(x,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No provider data available"})]})]})]}),K&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-[#ff6b35]"})}),(0,a.jsx)("div",{className:"text-right",children:et&&(0,a.jsxs)("div",{className:"flex items-center text-sm ".concat(et.isPositive?"text-red-500":"text-green-500"),children:[et.isPositive?(0,a.jsx)(i.A,{className:"h-4 w-4 mr-1"}):(0,a.jsx)(l,{className:"h-4 w-4 mr-1"}),et.percentage.toFixed(1),"%"]})})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:Y(K.total_cost)}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total spend"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[Y(K.average_cost_per_request)," avg per request"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(K.success_rate>=95?"bg-green-50":"bg-red-50"),children:(0,a.jsx)(m.A,{className:"h-5 w-5 ".concat(K.success_rate>=95?"text-green-600":"text-red-600")})}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center text-sm ".concat(K.success_rate>=95?"text-green-500":"text-red-500"),children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),K.success_rate>=95?"Excellent":"Needs attention"]})})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold text-gray-900",children:[K.success_rate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Success rate"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[$(K.successful_requests)," of ",$(K.total_requests)," requests"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-purple-500",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Tokens"]})})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:$(K.total_input_tokens+K.total_output_tokens)}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total tokens"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[$(K.total_input_tokens)," input • ",$(K.total_output_tokens)," output"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-indigo-50 rounded-lg",children:(0,a.jsx)(i.A,{className:"h-5 w-5 text-indigo-600"})}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center text-sm ".concat(Q>W?"text-red-500":"text-green-500"),children:[Q>W?(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}):(0,a.jsx)(m.A,{className:"h-4 w-4 mr-1"}),Q>W?"Over budget":"On track"]})})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:Y(Q)}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Projected monthly"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Budget: ",Y(W)]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Provider Performance"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Detailed breakdown by provider"})]}),(null==e?void 0:e.grouped_data.length)?(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Provider"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Cost"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Requests"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Share"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-50",children:e.grouped_data.sort((e,s)=>s.cost-e.cost).map((e,s)=>{let t=["#ff6b35","#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6"],r=(e.cost/((null==K?void 0:K.total_cost)||1)*100).toFixed(1);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-3",style:{backgroundColor:t[s%t.length]}}),(0,a.jsx)("span",{className:"font-medium text-gray-900 capitalize",children:e.name})]})}),(0,a.jsx)("td",{className:"py-4 text-right font-semibold text-gray-900",children:Y(e.cost)}),(0,a.jsx)("td",{className:"py-4 text-right text-gray-600",children:$(e.requests)}),(0,a.jsx)("td",{className:"py-4 text-right",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[r,"%"]})})]},e.name)})})]})}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(x,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No provider data available"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Top Models"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Most expensive models by cost"})]}),(null==t?void 0:t.grouped_data.length)?(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Model"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Cost"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Requests"}),(0,a.jsx)("th",{className:"text-right py-3 text-sm font-medium text-gray-600",children:"Avg/Request"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-50",children:t.grouped_data.sort((e,s)=>s.cost-e.cost).slice(0,5).map((e,s)=>{let t=["#ff6b35","#3b82f6","#10b981","#f59e0b","#ef4444"],r=e.cost/Math.max(e.requests,1);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-3",style:{backgroundColor:t[s%t.length]}}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:e.name.length>20?e.name.substring(0,20)+"...":e.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[$(e.input_tokens+e.output_tokens)," tokens"]})]})]})}),(0,a.jsx)("td",{className:"py-4 text-right font-semibold text-gray-900",children:Y(e.cost)}),(0,a.jsx)("td",{className:"py-4 text-right text-gray-600",children:$(e.requests)}),(0,a.jsx)("td",{className:"py-4 text-right text-gray-600",children:Y(r)})]},e.name)})})]})}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("p",{children:"No model data available"})]})]})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Performance Insights"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-4 rounded-lg bg-green-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:K?$(K.total_input_tokens+K.total_output_tokens):0}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Tokens Processed"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:K?"".concat($(K.total_input_tokens)," in • ").concat($(K.total_output_tokens)," out"):""})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-lg bg-blue-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:K?$(K.total_requests):0}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total API Requests"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:K?"".concat((K.total_requests/parseInt(M)).toFixed(1)," per day avg"):""})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-lg bg-purple-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:K?Y(K.average_cost_per_request):"$0.00"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Average Cost per Request"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"Across all providers"})]})]})]}),(X.length>0||ee.length>0)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[X.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mr-2 text-[#ff6b35]"}),"Alerts & Warnings"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Important notifications about your usage"})]}),(0,a.jsx)("div",{className:"space-y-4",children:X.map((e,s)=>(0,a.jsx)("div",{className:"p-4 rounded-xl border-l-4 ".concat("danger"===e.type?"border-red-500 bg-red-50":"warning"===e.type?"border-yellow-500 bg-yellow-50":"border-blue-500 bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-3 mt-0.5 ".concat("danger"===e.type?"text-red-600":"warning"===e.type?"text-yellow-600":"text-blue-600")}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),e.value&&(0,a.jsx)("p",{className:"text-lg font-bold mt-2 ".concat("danger"===e.type?"text-red-600":"warning"===e.type?"text-yellow-600":"text-blue-600"),children:e.value})]})]})},s))})]}),ee.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-yellow-500"}),"Smart Recommendations"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"AI-powered optimization suggestions"})]}),(0,a.jsx)("div",{className:"space-y-4",children:ee.map((e,s)=>(0,a.jsx)("div",{className:"p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-white shadow-sm mr-4",children:(0,a.jsx)(e.icon,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),e.potential_savings&&(0,a.jsxs)("div",{className:"mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800",children:[(0,a.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Save ",Y(e.potential_savings)]})]})]})},s))})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Budget Management"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Monitor and control your spending"})]}),(0,a.jsx)("button",{onClick:()=>H(!G),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200",children:G?"Hide Settings":"Configure"})]}),G&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-xl",children:[(0,a.jsxs)("div",{className:"max-w-xs",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Monthly Budget (USD)"}),(0,a.jsx)("input",{type:"number",value:W,onChange:e=>z(parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",placeholder:"100.00",step:"0.01",min:"0"})]}),(0,a.jsxs)("div",{className:"mt-3 text-xs text-gray-600",children:[(0,a.jsx)("p",{children:"• Alerts trigger at 80% (warning) and 100% (danger) of budget"}),(0,a.jsxs)("p",{children:["• Current projection: ",(0,a.jsx)("strong",{children:Y(Q)})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-blue-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:Y(W)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Monthly Budget"})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-orange-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-[#ff6b35]",children:Y(Q)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Projected Spend"})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-green-50",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:Y(Math.max(0,W-Q))}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:Q>W?"Over Budget":"Remaining"})]})]})]}),es&&(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 flex items-center",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Period Comparison"]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Performance vs previous period"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Cost Change"}),(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat((null==et?void 0:et.isPositive)?"text-red-600":"text-green-600"),children:[(null==et?void 0:et.isPositive)?"+":"-",null==et?void 0:et.percentage.toFixed(1),"%"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[Y(es.total_cost)," → ",Y((null==K?void 0:K.total_cost)||0)]})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Request Volume"}),(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat((null==ea?void 0:ea.isPositive)?"text-green-600":"text-red-600"),children:[(null==ea?void 0:ea.isPositive)?"+":"-",null==ea?void 0:ea.percentage.toFixed(1),"%"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[$(es.total_requests)," → ",$((null==K?void 0:K.total_requests)||0)]})]}),(0,a.jsxs)("div",{className:"text-center p-4 rounded-xl bg-gray-50",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-600 mb-2",children:"Efficiency"}),(0,a.jsx)("div",{className:"text-2xl font-bold ".concat(((null==K?void 0:K.average_cost_per_request)||0)<es.average_cost_per_request?"text-green-600":"text-red-600"),children:((null==K?void 0:K.average_cost_per_request)||0)<es.average_cost_per_request?"↑":"↓"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:[Y(es.average_cost_per_request)," → ",Y((null==K?void 0:K.average_cost_per_request)||0)]})]})]})]})]})})}function y(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},s))})]}),children:(0,a.jsx)(b,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(18959)),_N_E=e.O()}]);