(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1459],{4484:()=>{},5646:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=r(s(19936));t.PostgrestClient=i.default;let n=r(s(35068));t.PostgrestQueryBuilder=n.default;let a=r(s(83280));t.PostgrestFilterBuilder=a.default;let o=r(s(57156));t.PostgrestTransformBuilder=o.default;let l=r(s(9286));t.PostgrestBuilder=l.default;let h=r(s(41971));t.PostgrestError=h.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:h.default}},9286:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(92410)),n=r(s(41971));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,r;let i=null,a=null,o=null,l=e.status,h=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let r=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),n=null==(s=e.headers.get("content-range"))?void 0:s.split("/");r&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(i={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,h="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(a=[],i=null,l=200,h="OK")}catch(s){404===e.status&&""===t?(l=204,h="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null==(r=null==i?void 0:i.details)?void 0:r.includes("0 rows"))&&(i=null,l=200,h="OK"),i&&this.shouldThrowOnError)throw new n.default(i)}return{error:i,data:a,count:o,status:l,statusText:h}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,r;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(s=null==e?void 0:e.stack)?s:""}`,hint:"",code:`${null!=(r=null==e?void 0:e.code)?r:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},10182:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},13418:(e,t,s)=>{"use strict";let r,i;s.d(t,{createBrowserClient:()=>eM});var n,a,o,l,h,c,u,d,f,p,m=s(80414);let{PostgrestClient:g,PostgrestQueryBuilder:v,PostgrestFilterBuilder:b,PostgrestTransformBuilder:y,PostgrestBuilder:w,PostgrestError:k}=s(5646),_="undefined"==typeof window?s(20294):window.WebSocket,j={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(n||(n={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(a||(a={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(o||(o={})),(l||(l={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(h||(h={}));class E{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){let r=t.getUint8(1),i=t.getUint8(2),n=this.HEADER_LENGTH+2,a=s.decode(e.slice(n,n+r));n+=r;let o=s.decode(e.slice(n,n+i));return n+=i,{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(n,e.byteLength)))}}}class O{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(c||(c={}));let P=(e,t,s={})=>{var r;let i=null!=(r=s.skipTypes)?r:[];return Object.keys(t).reduce((s,r)=>(s[r]=T(r,e,t,i),s),{})},T=(e,t,s,r)=>{let i=t.find(t=>t.name===e),n=null==i?void 0:i.type,a=s[e];return n&&!r.includes(n)?$(n,a):C(a)},$=(e,t)=>{if("_"===e.charAt(0))return U(t,e.slice(1,e.length));switch(e){case c.bool:return S(t);case c.float4:case c.float8:case c.int2:case c.int4:case c.int8:case c.numeric:case c.oid:return A(t);case c.json:case c.jsonb:return R(t);case c.timestamp:return x(t);case c.abstime:case c.date:case c.daterange:case c.int4range:case c.int8range:case c.money:case c.reltime:case c.text:case c.time:case c.timestamptz:case c.timetz:case c.tsrange:case c.tstzrange:default:return C(t)}},C=e=>e,S=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},A=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},R=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},U=(e,t)=>{if("string"!=typeof e)return e;let s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r,i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(e){r=i?i.split(","):[]}return r.map(e=>$(t,e))}return e},x=e=>"string"==typeof e?e.replace(" ","T"):e,L=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class I{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null==(s=this.receivedResp)?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(u||(u={}));class B{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=B.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=B.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],r()}),this.channel._on(s.diff,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=B.syncDiff(this.state,e,t,s),r())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){let i=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(i,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let s=i[e];if(s){let r=t.map(e=>e.presence_ref),i=s.map(e=>e.presence_ref),n=t.filter(e=>0>i.indexOf(e.presence_ref)),l=s.filter(e=>0>r.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(i,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){let{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(t,r)=>{var i;let n=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(r),n.length>0){let s=e[t].map(e=>e.presence_ref),r=n.filter(e=>0>s.indexOf(e.presence_ref));e[t].unshift(...r)}s(t,n,r)}),this.map(n,(t,s)=>{let i=e[t];if(!i)return;let n=s.map(e=>e.presence_ref);i=i.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=i,r(t,i,s),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,s)=>{let r=e[s];return"metas"in r?t[s]=r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(d||(d={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(f||(f={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(p||(p={}));class D{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=a.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new I(this,o.join,this.params,this.timeout),this.rejoinTimer=new O(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=a.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=a.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=a.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=a.errored,this.rejoinTimer.scheduleTimeout())}),this._on(o.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new B(this),this.broadcastEndpointURL=L(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:n,private:o}}=this.params;this._onError(t=>null==e?void 0:e(p.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(p.CLOSED));let l={},h={broadcast:i,presence:n,postgres_changes:null!=(r=null==(s=this.bindings.postgres_changes)?void 0:s.map(e=>e.filter))?r:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:h},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0===t){null==e||e(p.SUBSCRIBED);return}{let r=this.bindings.postgres_changes,i=null!=(s=null==r?void 0:r.length)?s:0,n=[];for(let s=0;s<i;s++){let i=r[s],{filter:{event:o,schema:l,table:h,filter:c}}=i,u=t&&t[s];if(u&&u.event===o&&u.schema===l&&u.table===h&&u.filter===c)n.push(Object.assign(Object.assign({},i),{id:u.id}));else{this.unsubscribe(),this.state=a.errored,null==e||e(p.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(p.SUBSCRIBED);return}}).receive("error",t=>{this.state=a.errored,null==e||e(p.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(p.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,i,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(i=null==(r=this.params)?void 0:r.config)?void 0:i.broadcast)?void 0:n.ack)||s("ok"),a.receive("ok",()=>s("ok")),a.receive("error",()=>s("error")),a.receive("timeout",()=>s("timed out"))});{let{event:i,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(s=t.timeout)?s:this.timeout);return await (null==(r=e.body)?void 0:r.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=a.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(o.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(s=>{let r=new I(this,o.leave,{},e);r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){let r=new AbortController,i=setTimeout(()=>r.abort(),s),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),n}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new I(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;let n=e.toLocaleLowerCase(),{close:a,error:l,leave:h,join:c}=o;if(s&&[a,l,h,c].indexOf(n)>=0&&s!==this._joinRef())return;let u=this._onMessage(n,t,s);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null==(r=this.bindings.postgres_changes)||r.filter(e=>{var t,s,r;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(r=null==(s=e.filter)?void 0:s.event)?void 0:r.toLocaleLowerCase())===n}).map(e=>e.callback(u,s)):null==(i=this.bindings[n])||i.filter(e=>{var s,r,i,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null==(s=e.filter)?void 0:s.event;return n&&(null==(r=t.ids)?void 0:r.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let s=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===s||s===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:s,commit_timestamp:r,type:i,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:s,commit_timestamp:r,eventType:i,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,s)})}_isClosed(){return this.state===a.closed}_isJoined(){return this.state===a.joined}_isJoining(){return this.state===a.joining}_isLeaving(){return this.state===a.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){let r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){let s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var r;return!((null==(r=e.type)?void 0:r.toLocaleLowerCase())===s&&D.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(o.close,{},e)}_onError(e){this._on(o.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=a.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=P(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=P(e.columns,e.old_record)),t}}let N=()=>{},M=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class H{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=j,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=N,this.ref=0,this.logger=N,this.conn=null,this.sendBuffer=[],this.serializer=new E,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${l.websocket}`,this.httpEndpoint=L(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null==(r=null==t?void 0:t.params)?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new O(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=_),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new J(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case n.connecting:return h.Connecting;case n.open:return h.Open;case n.closing:return h.Closing;default:return h.Closed}}isConnected(){return this.connectionState()===h.Open}channel(e,t={config:{}}){let s=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===s);if(r)return r;{let s=new D(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){let{topic:t,event:s,payload:r,ref:i}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(o.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:i}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${i&&"("+i+")"||""}`,r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(o.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([M],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class J{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=n.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class F extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function q(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class z extends F{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class K extends F{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let G=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},W=()=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(s.bind(s,92410))).Response:Response}),V=e=>{if(Array.isArray(e))return e.map(e=>V(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,s])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=V(s)}),t};var Q=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};let X=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Y=(e,t,s)=>Q(void 0,void 0,void 0,function*(){e instanceof(yield W())&&!(null==s?void 0:s.noResolveJson)?e.json().then(s=>{t(new z(X(s),e.status||500))}).catch(e=>{t(new K(X(e),e))}):t(new K(X(e),e))}),Z=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),s))};function ee(e,t,s,r,i,n){return Q(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(s,Z(t,r,i,n)).then(e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>Y(e,o,r))})})}function et(e,t,s,r){return Q(this,void 0,void 0,function*(){return ee(e,"GET",t,s,r)})}function es(e,t,s,r,i){return Q(this,void 0,void 0,function*(){return ee(e,"POST",t,r,i,s)})}function er(e,t,s,r,i){return Q(this,void 0,void 0,function*(){return ee(e,"DELETE",t,r,i,s)})}var ei=s(44134).hp,en=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};let ea={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},eo={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class el{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=G(r)}uploadOrUpdate(e,t,s,r){return en(this,void 0,void 0,function*(){try{let i,n=Object.assign(Object.assign({},eo),r),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&s instanceof Blob?((i=new FormData).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o)),i.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?((i=s).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o))):(i=s,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));let l=this._removeEmptyFolders(t),h=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:i,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield c.json();if(c.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(e){if(q(e))return{data:null,error:e};throw e}})}upload(e,t,s){return en(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return en(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),n=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:eo.upsert},r),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s).append("cacheControl",t.cacheControl):(e=s,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(q(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return en(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");let i=yield es(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),n=new URL(this.url+i.url),a=n.searchParams.get("token");if(!a)throw new F("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}update(e,t,s){return en(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return en(this,void 0,void 0,function*(){try{return{data:yield es(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}copy(e,t,s){return en(this,void 0,void 0,function*(){try{return{data:{path:(yield es(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,s){return en(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield es(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,s){return en(this,void 0,void 0,function*(){try{let r=yield es(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}download(e,t){return en(this,void 0,void 0,function*(){let s=void 0!==(null==t?void 0:t.transform),r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=r?`?${r}`:"";try{let t=this._getFinalPath(e),r=yield et(this.fetch,`${this.url}/${s?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}info(e){return en(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield et(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:V(e),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}exists(e){return en(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,s,r){return Q(this,void 0,void 0,function*(){return ee(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(q(e)&&e instanceof K){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let s=this._getFinalPath(e),r=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&r.push(i);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${s}${o}`)}}}remove(e){return en(this,void 0,void 0,function*(){try{return{data:yield er(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}list(e,t,s){return en(this,void 0,void 0,function*(){try{let r=Object.assign(Object.assign(Object.assign({},ea),t),{prefix:e||""});return{data:yield es(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ei?ei.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let eh={"X-Client-Info":"storage-js/2.7.1"};var ec=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};class eu{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},eh),t),this.fetch=G(s)}listBuckets(){return ec(this,void 0,void 0,function*(){try{return{data:yield et(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}getBucket(e){return ec(this,void 0,void 0,function*(){try{return{data:yield et(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return ec(this,void 0,void 0,function*(){try{return{data:yield es(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return ec(this,void 0,void 0,function*(){try{return{data:yield function(e,t,s,r,i){return Q(this,void 0,void 0,function*(){return ee(e,"PUT",t,r,void 0,s)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}emptyBucket(e){return ec(this,void 0,void 0,function*(){try{return{data:yield es(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}deleteBucket(e){return ec(this,void 0,void 0,function*(){try{return{data:yield er(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(q(e))return{data:null,error:e};throw e}})}}class ed extends eu{constructor(e,t={},s){super(e,t,s)}from(e){return new el(this.url,this.headers,e,this.fetch)}}let ef="";ef="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let ep={headers:{"X-Client-Info":"supabase-js-".concat(ef,"/").concat("2.50.0")}},em={schema:"public"},eg={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ev={};var eb=s(92410);let ey=e=>{let t;return t=e||("undefined"==typeof fetch?eb.default:fetch),function(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return t(...s)}},ew=()=>"undefined"==typeof Headers?eb.Headers:Headers,ek=(e,t,s)=>{let r=ey(s),i=ew();return(s,n)=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new i(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization","Bearer ".concat(o)),r(s,Object.assign(Object.assign({},n),{headers:l}))})};var e_=s(44761);class ej extends e_.UJ{constructor(e){super(e)}}class eE{get functions(){return new m.FS(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ed(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.rest.rpc(e,t,s)}channel(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{config:{}};return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,s,r,i,n;return s=this,r=void 0,i=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:s}=yield this.auth.getSession();return null!=(t=null==(e=s.session)?void 0:e.access_token)?t:null},new(i||(i=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var s;t.done?e(t.value):((s=t.value)instanceof i?s:new i(function(e){e(s)})).then(a,o)}l((n=n.apply(s,r||[])).next())})}_initSupabaseAuthClient(e,t,s){let{autoRefreshToken:r,persistSession:i,detectSessionInUrl:n,storage:a,storageKey:o,flowType:l,lock:h,debug:c}=e,u={Authorization:"Bearer ".concat(this.supabaseKey),apikey:"".concat(this.supabaseKey)};return new ej({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),t),storageKey:o,autoRefreshToken:r,persistSession:i,detectSessionInUrl:n,storage:a,flowType:l,lock:h,debug:c,fetch:s,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new H(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==s?this.changedAccessToken=s:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}constructor(e,t,s){var r,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o="sb-".concat(a.hostname.split(".")[0],"-auth-token"),l=function(e,t){var s,r;let{db:i,auth:n,realtime:a,global:o}=e,{db:l,auth:h,realtime:c,global:u}=t,d={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},h),n),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!=(s=null==u?void 0:u.headers)?s:{}),null!=(r=null==o?void 0:o.headers)?r:{})}),accessToken:()=>{var e,t,s,r;return e=this,t=void 0,r=function*(){return""},new(s=void 0,s=Promise)(function(i,n){function a(e){try{l(r.next(e))}catch(e){n(e)}}function o(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:em,realtime:ev,auth:Object.assign(Object.assign({},eg),{storageKey:o}),global:ep});this.storageKey=null!=(r=l.auth.storageKey)?r:"",this.headers=null!=(i=l.global.headers)?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error("@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.".concat(String(t)," is not possible"))}})):this.auth=this._initSupabaseAuthClient(null!=(n=l.auth)?n:{},this.headers,l.global.fetch),this.fetch=ek(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new g(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}}let eO=(e,t,s)=>new eE(e,t,s);var eP=s(41049);function eT(){return"undefined"!=typeof window&&void 0!==window.document}let e$={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},eC=/^(.*)[.](0|[1-9][0-9]*)$/;function eS(e,t){if(e===t)return!0;let s=e.match(eC);return!!s&&s[1]===t}function eA(e,t,s){let r=s??3180,i=encodeURIComponent(t);if(i.length<=r)return[{name:e,value:t}];let n=[];for(;i.length>0;){let e=i.slice(0,r),t=e.lastIndexOf("%");t>r-3&&(e=e.slice(0,t));let s="";for(;e.length>0;)try{s=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}n.push(s),i=i.slice(e.length)}return n.map((t,s)=>({name:`${e}.${s}`,value:t}))}async function eR(e,t){let s=await t(e);if(s)return s;let r=[];for(let s=0;;s++){let i=`${e}.${s}`,n=await t(i);if(!n)break;r.push(n)}return r.length>0?r.join(""):null}let eU="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ex=" 	\n\r=".split(""),eL=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<ex.length;t+=1)e[ex[t].charCodeAt(0)]=-2;for(let t=0;t<eU.length;t+=1)e[eU[t].charCodeAt(0)]=t;return e})();function eI(e){let t=[],s=0,r=0;if(function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(r,t)}}(e,e=>{for(s=s<<8|e,r+=8;r>=6;){let e=s>>r-6&63;t.push(eU[e]),r-=6}}),r>0)for(s<<=6-r,r=6;r>=6;){let e=s>>r-6&63;t.push(eU[e]),r-=6}return t.join("")}function eB(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i=0,n=0;for(let t=0;t<e.length;t+=1){let a=eL[e.charCodeAt(t)];if(a>-1)for(i=i<<6|a,n+=6;n>=8;)(function(e,t,s){if(0===t.utf8seq){if(e<=127)return s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}})(i>>n-8&255,r,s),n-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let eD="base64-";async function eN({getAll:e,setAll:t,setItems:s,removedItems:r},i){let n=i.cookieEncoding,a=i.cookieOptions??null,o=await e([...s?Object.keys(s):[],...r?Object.keys(r):[]]),l=o?.map(({name:e})=>e)||[],h=Object.keys(r).flatMap(e=>l.filter(t=>eS(t,e))),c=Object.keys(s).flatMap(e=>{let t=new Set(l.filter(t=>eS(t,e))),r=s[e];"base64url"===n&&(r=eD+eI(r));let i=eA(e,r);return i.forEach(e=>{t.delete(e.name)}),h.push(...t),i}),u={...e$,...a,maxAge:0},d={...e$,...a,maxAge:e$.maxAge};delete u.name,delete d.name,await t([...h.map(e=>({name:e,value:"",options:u})),...c.map(({name:e,value:t})=>({name:e,value:t,options:d}))])}function eM(e,t,s){let r=s?.isSingleton===!0||(!s||!("isSingleton"in s))&&eT();if(r&&i)return i;if(!e||!t)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:n}=function(e,t){let s,r,i=e.cookies??null,n=e.cookieEncoding,a={},o={};if(i)if("get"in i){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,s)=>`${e}.${s}`)]),s=[];for(let e=0;e<t.length;e+=1){let r=await i.get(t[e]);(r||"string"==typeof r)&&s.push({name:t[e],value:r})}return s};if(s=async t=>await e(t),"set"in i&&"remove"in i)r=async e=>{for(let t=0;t<e.length;t+=1){let{name:s,value:r,options:n}=e[t];r?await i.set(s,r,n):await i.remove(s,n)}};else if(t)r=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in i)if(s=async()=>await i.getAll(),"setAll"in i)r=i.setAll;else if(t)r=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${eT()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&eT()){let e=()=>{let e=(0,eP.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};s=()=>e(),r=e=>{e.forEach(({name:e,value:t,options:s})=>{document.cookie=(0,eP.lK)(e,t,s)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else s=()=>[],r=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:s,setAll:r,setItems:a,removedItems:o,storage:{isServer:!0,getItem:async e=>{if("string"==typeof a[e])return a[e];if(o[e])return null;let t=await s([e]),r=await eR(e,async e=>{let s=t?.find(({name:t})=>t===e)||null;return s?s.value:null});if(!r)return null;let i=r;return"string"==typeof r&&r.startsWith(eD)&&(i=eB(r.substring(eD.length))),i},setItem:async(t,i)=>{t.endsWith("-code-verifier")&&await eN({getAll:s,setAll:r,setItems:{[t]:i},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:n}),a[t]=i,delete o[t]},removeItem:async e=>{delete a[e],o[e]=!0}}}:{getAll:s,setAll:r,setItems:a,removedItems:o,storage:{isServer:!1,getItem:async e=>{let t=await s([e]),r=await eR(e,async e=>{let s=t?.find(({name:t})=>t===e)||null;return s?s.value:null});if(!r)return null;let i=r;return r.startsWith(eD)&&(i=eB(r.substring(eD.length))),i},setItem:async(t,i)=>{let a=await s([t]),o=new Set((a?.map(({name:e})=>e)||[]).filter(e=>eS(e,t))),l=i;"base64url"===n&&(l=eD+eI(i));let h=eA(t,l);h.forEach(({name:e})=>{o.delete(e)});let c={...e$,...e?.cookieOptions,maxAge:0},u={...e$,...e?.cookieOptions,maxAge:e$.maxAge};delete c.name,delete u.name;let d=[...[...o].map(e=>({name:e,value:"",options:c})),...h.map(({name:e,value:t})=>({name:e,value:t,options:u}))];d.length>0&&await r(d)},removeItem:async t=>{let i=await s([t]),n=(i?.map(({name:e})=>e)||[]).filter(e=>eS(e,t)),a={...e$,...e?.cookieOptions,maxAge:0};delete a.name,n.length>0&&await r(n.map(e=>({name:e,value:"",options:a})))}}}}({...s,cookieEncoding:s?.cookieEncoding??"base64url"},!1),a=eO(e,t,{...s,global:{...s?.global,headers:{...s?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createBrowserClient"}},auth:{...s?.auth,...s?.cookieOptions?.name?{storageKey:s.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:eT(),detectSessionInUrl:eT(),persistSession:!0,storage:n}});return r&&(i=a),a}s(4484)},19936:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(35068)),n=r(s(83280)),a=s(85171);class o{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:i}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);s||r?(a=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let h=Object.assign({},this.headers);return i&&(h.Prefer=`count=${i}`),new n.default({method:a,url:l,headers:h,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},35068:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(83280));class n{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){let r=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!r?"":('"'===e&&(r=!r),e)).join("");return this.url.searchParams.set("select",n),s&&(this.headers.Prefer=`count=${s}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){let r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){let a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),r&&a.push(`count=${r}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},41971:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class s extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=s},57156:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(9286));class n extends i.default{select(e){let t=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:i=r}={}){let n=i?`${i}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){let r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){let i=void 0===r?"offset":`${r}.offset`,n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:i=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},83280:function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(57156));class n extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let s=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let i="";"plain"===r?i="pl":"phrase"===r?i="ph":"websearch"===r&&(i="w");let n=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){let r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}t.default=n},85171:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let r=s(10182);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${r.version}`}},92410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>i});var r=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==s.g)return s.g;throw Error("unable to locate global object")}();let i=r.fetch,n=r.fetch.bind(r),a=r.Headers,o=r.Request,l=r.Response}}]);