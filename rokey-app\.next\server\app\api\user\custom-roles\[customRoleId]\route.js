(()=>{var e={};e.id=5652,e.ids=[1489,5652],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>u,createSupabaseServerClientOnRequest:()=>a});var s=r(34386),o=r(44999);async function a(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},48930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>d,PUT:()=>l});var o=r(96559),a=r(48088),u=r(37719),i=r(32190),n=r(2507),c=r(45697);let p=c.z.object({name:c.z.string().trim().min(1,"Name is required").max(100,"Name must be 100 characters or less").optional(),description:c.z.string().trim().max(500,"Description must be 500 characters or less").optional().nullable()}).refine(e=>Object.keys(e).length>0,{message:"At least one field (name or description) must be provided for an update."});async function l(e,{params:t}){let r,s=await (0,n.createSupabaseServerClientOnRequest)(),{data:{user:o},error:a}=await s.auth.getUser(),{customRoleId:u}=await t;if(a||!o)return i.NextResponse.json({error:"Unauthorized. Please log in."},{status:401});let c=o.id;if(!u||"string"!=typeof u)return i.NextResponse.json({error:"Invalid Custom Role ID format."},{status:400});try{r=await e.json()}catch(e){return i.NextResponse.json({error:"Invalid JSON request body."},{status:400})}let l=p.safeParse(r);if(!l.success)return i.NextResponse.json({error:"Invalid request body.",issues:l.error.flatten().fieldErrors},{status:400});let d=l.data;try{let{data:e,error:t}=await s.from("user_custom_roles").update({...d,updated_at:new Date().toISOString()}).eq("id",u).eq("user_id",c).select().single();if(t){if("PGRST116"===t.code)return i.NextResponse.json({error:"Custom role not found or you do not own this role."},{status:404});return i.NextResponse.json({error:"Failed to update custom role.",details:t.message},{status:500})}if(!e)return i.NextResponse.json({error:"Custom role not found after update attempt."},{status:404});return i.NextResponse.json(e,{status:200})}catch(e){return i.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}async function d(e,{params:t}){let r=await (0,n.createSupabaseServerClientOnRequest)(),{data:{user:s},error:o}=await r.auth.getUser(),{customRoleId:a}=await t;if(o||!s)return i.NextResponse.json({error:"Unauthorized. Please log in."},{status:401});let u=s.id;if(!a||"string"!=typeof a)return i.NextResponse.json({error:"Invalid Custom Role ID format."},{status:400});try{let{error:e,count:t}=await r.from("user_custom_roles").delete({count:"exact"}).eq("id",a).eq("user_id",u);if(e)return i.NextResponse.json({error:"Failed to delete custom role.",details:e.message},{status:500});if(0===t)return i.NextResponse.json({error:"Custom role not found, you do not own this role, or it was already deleted."},{status:404});return i.NextResponse.json({message:"Custom role deleted successfully. Associated API key assignments may need to be manually reviewed or will be handled by routing logic gracefullly."},{status:200})}catch(e){return i.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/user/custom-roles/[customRoleId]/route",pathname:"/api/user/custom-roles/[customRoleId]",filename:"route",bundlePath:"app/api/user/custom-roles/[customRoleId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\custom-roles\\[customRoleId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:h}=m;function y(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410,5697],()=>r(48930));module.exports=s})();