"use strict";(()=>{var e={};e.id=9064,e.ids=[9064],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10640:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>u});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),p=r(87846),c=r(62706);async function u(e){try{let{action:t,userId:r,configId:s,promptText:a,responseData:o}=await e.json();if(!r||!s)return i.NextResponse.json({error:"userId and configId are required"},{status:400});let n=await (0,c.cr)(r);if("search"===t){if(!a)return i.NextResponse.json({error:"promptText is required for search"},{status:400});let e=await p.R.searchCache({promptText:a,modelUsed:"test-model",providerUsed:"test-provider",temperature:.7,maxTokens:1e3,metadata:{test:!0}},r,s,n);if(e)return i.NextResponse.json({success:!0,cacheHit:!0,data:{similarity:e.similarity,promptText:e.promptText,responseData:e.responseData,hitCount:e.hitCount,createdAt:e.createdAt}});return i.NextResponse.json({success:!0,cacheHit:!1,message:"No cache hit found"})}if("store"===t){if(!a||!o)return i.NextResponse.json({error:"promptText and responseData are required for store"},{status:400});let e=await p.R.storeCache({promptText:a,modelUsed:"test-model",providerUsed:"test-provider",temperature:.7,maxTokens:1e3,metadata:{test:!0}},{responseData:o,tokensPrompt:100,tokensCompletion:200,cost:.001},r,s,n);return i.NextResponse.json({success:!0,stored:e,message:e?"Response stored in cache":"Failed to store in cache (tier restrictions or error)"})}if("stats"===t){let e=await p.R.getCacheStats(r,s,7);return i.NextResponse.json({success:!0,stats:e,userTier:n})}if("cleanup"===t){let e=await p.R.cleanupExpiredCache();return i.NextResponse.json({success:!0,deletedCount:e,message:`Cleaned up ${e} expired cache entries`})}return i.NextResponse.json({error:"Invalid action. Use: search, store, stats, or cleanup"},{status:400})}catch(e){return i.NextResponse.json({error:"Internal server error",details:e.message},{status:500})}}async function d(e){return i.NextResponse.json({message:"Semantic Cache Test Endpoint",usage:{"POST /api/test/semantic-cache":{description:"Test semantic cache functionality",actions:{search:"Search for cached responses",store:"Store a response in cache",stats:"Get cache statistics",cleanup:"Clean up expired cache entries"},example:{action:"search",userId:"user-uuid",configId:"config-uuid",promptText:"Hello, how are you?"}}}})}let x=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test/semantic-cache/route",pathname:"/api/test/semantic-cache",filename:"route",bundlePath:"app/api/test/semantic-cache/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\test\\semantic-cache\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:m}=x;function f(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,9805],()=>r(10640));module.exports=s})();