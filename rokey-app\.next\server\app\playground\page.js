(()=>{var e={};e.id=3882,e.ids=[3882],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14566:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))})},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},17712:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19682:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))})},21590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx","default")},23810:(e,t,r)=>{Promise.resolve().then(r.bind(r,48976))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32675:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687),o=r(43210),n=r(14689);let s=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))});function i({text:e,className:t="",size:r="sm",variant:i="default",title:l="Copy to clipboard"}){let[c,d]=(0,o.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(e),d(!0),setTimeout(()=>d(!1),2e3)}catch(r){let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy"),d(!0),setTimeout(()=>d(!1),2e3)}catch(e){}document.body.removeChild(t)}},u={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,a.jsx)("button",{onClick:m,className:`
        ${{sm:"p-1.5",md:"p-2",lg:"p-2.5"}[r]}
        ${{default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[i]}
        rounded transition-all duration-200 cursor-pointer
        ${c?"text-green-600":""}
        ${t}
      `,title:c?"Copied!":l,children:c?(0,a.jsx)(n.A,{className:`${u[r]} stroke-2`}):(0,a.jsx)(s,{className:`${u[r]} stroke-2`})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(37413),o=r(47417);function n(){return(0,a.jsx)("div",{className:"h-screen flex bg-gray-50",children:(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200 p-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-32 rounded"}),(0,a.jsx)(o.ConfigSelectorSkeleton,{})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-20 rounded"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-8 rounded-full"})]})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,a.jsx)("div",{className:"h-full flex justify-center",children:(0,a.jsx)("div",{className:"w-full max-w-4xl px-6",children:(0,a.jsx)(o.MessageSkeleton,{})})})}),(0,a.jsxs)("div",{className:"w-80 bg-white border-l border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-6 w-24 rounded mb-2"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-full rounded"})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)("div",{className:"p-3 rounded-xl border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-2",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-16 rounded"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-12 rounded"})]})]})},t))})})]})]}),(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-12 w-full rounded-xl"})})})]})})}},47417:(e,t,r)=>{"use strict";r.d(t,{AnalyticsSkeleton:()=>l,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>o,MyModelsSkeleton:()=>s,RoutingSetupSkeleton:()=>i});var a=r(12907);(0,a.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let l=(0,a.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},48935:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},48976:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ea});var a=r(60687),o=r(43210),n=r.n(o);let s=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),i=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),l=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),c=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),d=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),m=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))}),u=(0,o.lazy)(()=>r.e(317).then(r.bind(r,317))),g=()=>(0,a.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function h({content:e,className:t=""}){return(0,a.jsx)(o.Suspense,{fallback:(0,a.jsx)(g,{}),children:(0,a.jsx)(u,{content:e,className:t})})}var p=r(32675),x=r(49579),f=r(27010);let b=new Map;function y({configId:e,onRetry:t,className:r="",disabled:n=!1}){let[s,i]=(0,o.useState)(!1),[l,c]=(0,o.useState)([]),[d,m]=(0,o.useState)(!1),[u,g]=(0,o.useState)(!1),h=(0,o.useRef)(null),p=(0,o.useCallback)(async(t=!0)=>{if(e){if(t){let t=b.get(e);if(t&&Date.now()-t.timestamp<3e5){c(t.keys),g(!0);return}}m(!0);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(t.ok){let r=(await t.json()).filter(e=>"active"===e.status);b.set(e,{keys:r,timestamp:Date.now()}),c(r),g(!0)}}catch(e){}finally{m(!1)}}},[e]),y=e=>{i(!1),t(e)};return(0,a.jsxs)("div",{className:`relative ${r}`,ref:h,children:[(0,a.jsxs)("button",{onClick:()=>{s||0!==l.length||u||p(!0),i(!s)},disabled:n,className:`
          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer
          ${n?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20"}
        `,title:"Retry with different model",children:[(0,a.jsx)(x.A,{className:`w-4 h-4 stroke-2 ${d?"animate-spin":""}`}),(0,a.jsx)(f.A,{className:"w-3 h-3 stroke-2"})]}),s&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),p(!1)},disabled:d,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,a.jsx)(x.A,{className:`w-3 h-3 ${d?"animate-spin":""}`})})]}),(0,a.jsxs)("button",{onClick:()=>y(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:"Retry with same model"})]}),(l.length>0||d)&&(0,a.jsx)("div",{className:"border-t border-gray-100 my-1"}),d&&(0,a.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Loading models..."})]}),l.map(e=>(0,a.jsxs)("button",{onClick:()=>y(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:d,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!d&&0===l.length&&u&&(0,a.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),l.length>0&&!d&&(0,a.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[l.length," model",1!==l.length?"s":""," available"]}),(()=>{let t=b.get(e);return t&&Date.now()-t.timestamp<3e5?(0,a.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}var w=r(76180),v=r.n(w);let k=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))}),j=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var C=r(19682),N=r(48935),S=r(62392);let T=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),A=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var E=r(71178),L=r(64364),M=r(50942),R=r(58089),_=r(14566),P=r(93635);let O=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))}),$=o.forwardRef(function({title:e,titleId:t,...r},a){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{fillRule:"evenodd",d:"M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",clipRule:"evenodd"}))}),I={initializing:{icon:k,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:j,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:x.A,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:C.A,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:N.A,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:S.A,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:T,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:A,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:E.A,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:L.A,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:M.A,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function D({currentStage:e,isStreaming:t=!1,className:r="",onStageChange:n,orchestrationStatus:s}){let[i,l]=(0,o.useState)(e),[c,d]=(0,o.useState)(!1),[m,u]=(0,o.useState)(0),[g,h]=(0,o.useState)(""),[p,f]=(0,o.useState)(1),b=[{bgColor:"bg-gradient-to-r from-blue-50 to-indigo-50",iconColor:"text-blue-600",borderColor:"border-blue-200/60",glowColor:"shadow-blue-200/50",gradientFrom:"from-blue-400",gradientTo:"to-indigo-400"},{bgColor:"bg-gradient-to-r from-purple-50 to-violet-50",iconColor:"text-purple-600",borderColor:"border-purple-200/60",glowColor:"shadow-purple-200/50",gradientFrom:"from-purple-400",gradientTo:"to-violet-400"},{bgColor:"bg-gradient-to-r from-indigo-50 to-blue-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-blue-400"},{bgColor:"bg-gradient-to-r from-cyan-50 to-teal-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-teal-400"},{bgColor:"bg-gradient-to-r from-teal-50 to-emerald-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-emerald-400"},{bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400"},{bgColor:"bg-gradient-to-r from-yellow-50 to-amber-50",iconColor:"text-yellow-600",borderColor:"border-yellow-200/60",glowColor:"shadow-yellow-200/50",gradientFrom:"from-yellow-400",gradientTo:"to-amber-400"},{bgColor:"bg-gradient-to-r from-orange-50 to-red-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-red-400"},{bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400"},{bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400"}],y=b[m%b.length],w=s?{...I[i],...y,icon:s.includes("\uD83D\uDD0D")||s.includes("detected")?j:s.includes("✅")||s.includes("complete")?R.A:s.includes("\uD83C\uDFAF")||s.includes("Selected")?$:s.includes("\uD83C\uDFD7️")||s.includes("workflow")?_.A:s.includes("\uD83E\uDD16")||s.includes("agent")?P.A:s.includes("\uD83D\uDC51")||s.includes("supervisor")?N.A:s.includes("\uD83D\uDCCB")||s.includes("Planning")?O:s.includes("\uD83D\uDE80")||s.includes("starting")?k:s.includes("\uD83D\uDD04")||s.includes("synthesizing")?x.A:I[i].icon}:I[i],C=w.icon;return(0,a.jsxs)("div",{className:`jsx-f56d70faa8a01b64 flex justify-start ${r}`,children:[(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,a.jsx)("div",{style:{animation:`spin ${1.2/p}s linear infinite`,borderTopColor:w.iconColor.replace("text-",""),filter:"drop-shadow(0 0 6px rgba(59, 130, 246, 0.4))"},className:"jsx-f56d70faa8a01b64 absolute -inset-2 w-10 h-10 rounded-full border-[3px] border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:`spin ${1.8/p}s linear infinite reverse`,borderTopColor:w.iconColor.replace("text-","").replace("600","400"),opacity:.7},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[2px] border-t-purple-400 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:`spin ${.8/p}s linear infinite`,borderTopColor:w.iconColor.replace("text-","").replace("600","300"),opacity:.5},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[1px] border-t-cyan-300 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{borderColor:w.iconColor.replace("text-","").replace("600","200"),opacity:.3,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border animate-pulse"}),(0,a.jsx)("div",{style:{boxShadow:`0 0 12px ${w.iconColor.replace("text-","")}40, 0 0 24px ${w.iconColor.replace("text-","")}20`},className:`jsx-f56d70faa8a01b64 relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ${w.bgColor} border-2 ${w.borderColor} shadow-lg backdrop-blur-sm`,children:(0,a.jsx)(C,{className:`jsx-f56d70faa8a01b64 w-3.5 h-3.5 transition-all duration-500 ${w.iconColor} ${c?"scale-125 rotate-12":"scale-100"} drop-shadow-lg`})})]}),(0,a.jsxs)("div",{className:`jsx-f56d70faa8a01b64 max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ${w.bgColor} ${w.borderColor} border ${w.glowColor} shadow-sm backdrop-blur-sm`,children:[(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 text-xs font-semibold transition-colors duration-500 ${w.iconColor} tracking-wide`,children:s||w.text}),t&&"typing"===i&&!s&&(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 ml-1.5 text-[10px] opacity-80 ${w.iconColor} font-medium`,children:"• Live"}),s&&(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 ml-1.5 text-[10px] opacity-80 ${w.iconColor} font-medium`,children:"• Orchestrating"})]})}),("generating"===i||"typing"===i)&&(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,a.jsx)("div",{style:{width:"typing"===i?"100%":"60%",animation:"typing"===i?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:`jsx-f56d70faa8a01b64 h-full rounded-full transition-all duration-1000 bg-gradient-to-r ${w.gradientFrom} ${w.gradientTo} relative overflow-hidden`,children:(0,a.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,a.jsx)(v(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}var z=r(17712);let H=({orchestrationComplete:e,onMaximize:t,isCanvasOpen:r,isCanvasMinimized:o})=>(0,a.jsxs)("div",{className:`flex justify-start group mb-16 mt-8 ${r&&!o?"-ml-96":""} ${r&&!o?"ml-8":""}`,children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)("div",{className:`${r&&!o?"max-w-[80%]":"max-w-[65%]"} relative`,children:(0,a.jsx)("div",{onClick:t,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(z.A,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,a.jsx)("p",{className:"text-xs opacity-90",children:e?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,a.jsxs)("div",{className:"flex-shrink-0",children:[!e&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),e&&(0,a.jsx)(L.A,{className:"w-5 h-5"})]})]})})})]});var F=r(50549),B=r(48427);let W={initializing:50,analyzing:150,routing:200,complexity_analysis:250,role_classification:300,preparing:150,connecting:200,generating:400,typing:0,finalizing:100,complete:0};var q=r(11016);class K{static getInstance(){return K.instance||(K.instance=new K),K.instance}trackParallelFlow(e){let t=`${e.provider}_${e.model}_parallel`;this.parallelMetrics||(this.parallelMetrics=new Map),this.parallelMetrics.has(t)||this.parallelMetrics.set(t,[]);let r=this.parallelMetrics.get(t);r.push({...e,timestamp:Date.now()}),r.length>this.maxSamples&&r.shift(),e.firstTokenTime}trackMessagingFlow(e){let t=`${e.provider}_${e.model}`;this.metrics.has(t)||this.metrics.set(t,[]);let r=this.metrics.get(t);r.push({timestamp:Date.now(),...e}),r.length>this.maxSamples&&r.shift(),this.logPerformanceInsights(e)}getStats(e,t){let r=`${e}_${t}`,a=this.metrics.get(r);if(!a||0===a.length)return null;let o=a.filter(e=>e.success);if(0===o.length)return null;let n=o.map(e=>e.timings.total),s=o.map(e=>e.timings.llmApiCall),i=o.map(e=>e.messageLength);return{provider:e,model:t,sampleCount:o.length,averageTotal:this.calculateAverage(n),averageLLM:this.calculateAverage(s),medianTotal:this.calculateMedian(n),medianLLM:this.calculateMedian(s),p95Total:this.calculatePercentile(n,95),p95LLM:this.calculatePercentile(s,95),minTotal:Math.min(...n),maxTotal:Math.max(...n),averageMessageLength:this.calculateAverage(i),streamingUsage:o.filter(e=>e.isStreaming).length/o.length,errorRate:(a.length-o.length)/a.length,recentTrend:this.calculateTrend(n.slice(-10))}}getSummary(){let e=new Set,t=[];for(let r of this.metrics.keys()){let[a,o]=r.split("_");e.add(a);let n=this.getStats(a,o);n&&t.push(n)}if(0===t.length)return{totalProviders:0,totalModels:0,overallAverageTime:0,fastestProvider:null,slowestProvider:null,recommendations:["No messaging data available yet"]};let r=this.calculateAverage(t.map(e=>e.averageTotal)),a=[...t].sort((e,t)=>e.averageTotal-t.averageTotal);return{totalProviders:e.size,totalModels:t.length,overallAverageTime:r,fastestProvider:a[0],slowestProvider:a[a.length-1],recommendations:this.generateRecommendations(t)}}generateRecommendations(e){let t=[],r=e.filter(e=>e.averageTotal>5e3);r.length>0&&t.push(`Consider switching from slow providers: ${r.map(e=>e.provider).join(", ")}`),e.filter(e=>e.streamingUsage<.5).length>0&&t.push("Enable streaming for better perceived performance");let a=e.filter(e=>e.errorRate>.1);a.length>0&&t.push(`High error rates detected for: ${a.map(e=>e.provider).join(", ")}`);let o=e.filter(e=>e.averageTotal<=2e3);return 0===o.length?t.push("No providers meeting 2s target - consider optimizing or switching providers"):t.push(`Fast providers (≤2s): ${o.map(e=>e.provider).join(", ")}`),t.length>0?t:["Performance looks good!"]}logPerformanceInsights(e){let{provider:t,model:r,timings:a,isStreaming:o,streamingMetrics:n}=e;o&&a.timeToFirstToken&&(a.timeToFirstToken<500||a.timeToFirstToken<1e3||a.timeToFirstToken,n&&n.averageTokenLatency),a.total,a.total,a.total,a.llmApiCall,a.llmApiCall,a.total,a.backendProcessing&&a.frontendProcessing&&(a.backendProcessing,a.total,a.frontendProcessing,a.total),o||a.total}calculateAverage(e){return e.reduce((e,t)=>e+t,0)/e.length}calculateMedian(e){let t=[...e].sort((e,t)=>e-t),r=Math.floor(t.length/2);return t.length%2==0?(t[r-1]+t[r])/2:t[r]}calculatePercentile(e,t){let r=[...e].sort((e,t)=>e-t),a=Math.ceil(t/100*r.length)-1;return r[Math.max(0,a)]}calculateTrend(e){if(e.length<5)return"stable";let t=e.slice(0,Math.floor(e.length/2)),r=e.slice(Math.floor(e.length/2)),a=this.calculateAverage(t),o=(this.calculateAverage(r)-a)/a*100;return o<-10?"improving":o>10?"degrading":"stable"}exportData(){let e={};for(let[t,r]of this.metrics.entries())e[t]=[...r];return e}clear(){this.metrics.clear()}constructor(){this.metrics=new Map,this.maxSamples=50,this.parallelMetrics=new Map}}class Z{static getInstance(){return Z.instance||(Z.instance=new Z),Z.instance}startRequest(e,t,r){this.timingData.set(e,{requestStart:performance.now(),tokenCount:0,provider:t,model:r})}markFirstToken(e){let t=this.timingData.get(e);if(!t)return null;let r=performance.now()-t.requestStart;return t.firstTokenReceived=performance.now(),r}trackToken(e){let t=this.timingData.get(e);t&&t.tokenCount++}completeStream(e){let t=this.timingData.get(e);if(!t||!t.firstTokenReceived)return null;let r=performance.now();t.streamComplete=r;let a=t.firstTokenReceived-t.requestStart,o=r-t.requestStart,n=r-t.firstTokenReceived,s=t.tokenCount>1?n/(t.tokenCount-1):0,i={timeToFirstToken:a,totalStreamTime:o,totalTokens:t.tokenCount,averageTokenLatency:s};return this.timingData.delete(e),i}getStatus(e){let t=this.timingData.get(e);return t?t.firstTokenReceived?t.streamComplete?"Complete":"Streaming in progress":"Waiting for first token":"Not tracked"}clear(){this.timingData.clear()}constructor(){this.timingData=new Map}}let V=K.getInstance(),U=Z.getInstance();function Y(){let e=V.getSummary();e.fastestProvider,e.slowestProvider,e.recommendations.forEach(e=>console.log(`   • ${e}`))}function J(){U.timingData?.size}function G(){}function X(){let e=setInterval(()=>{V.getSummary().totalProviders},3e4);globalThis.__performanceMonitoringInterval=e}function Q(){let e=globalThis.__performanceMonitoringInterval;e&&(clearInterval(e),delete globalThis.__performanceMonitoringInterval)}function ee(){let e=V.getSummary();0!==e.totalProviders&&(e.overallAverageTime<2e3||e.overallAverageTime<5e3||e.overallAverageTime,e.fastestProvider,e.slowestProvider)}"undefined"!=typeof globalThis&&(globalThis.logComprehensivePerformanceReport=Y,globalThis.logFirstTokenReport=J,globalThis.logGoogleStreamingDebug=G,globalThis.quickPerformanceCheck=ee,globalThis.startPerformanceMonitoring=X,globalThis.stopPerformanceMonitoring=Q,globalThis.performanceLogs={comprehensive:Y,firstToken:J,googleDebug:G,quick:ee,startMonitoring:X,stopMonitoring:Q});let et=(0,o.lazy)(()=>r.e(5563).then(r.bind(r,95563)).then(e=>({default:e.OrchestrationCanvas}))),er=n().memo(({chat:e,currentConversation:t,onLoadChat:r,onDeleteChat:o})=>{let n=t?.id===e.id;return(0,a.jsxs)("div",{className:`relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 ${n?"bg-orange-50 border border-orange-200":""}`,children:[(0,a.jsx)("button",{onClick:()=>r(e),className:"w-full text-left",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate mb-1",children:e.title}),e.last_message_preview&&(0,a.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:e.last_message_preview}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,a.jsxs)("span",{children:[e.message_count," messages"]}),(0,a.jsx)("span",{children:new Date(e.updated_at).toLocaleDateString()})]})]})})}),(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),o(e.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200",title:"Delete conversation",children:(0,a.jsx)(s,{className:"w-4 h-4"})})]})});function ea(){let{isCollapsed:e,isHovered:t,setHoverDisabled:r}=(0,F.c)(),{user:n}=(0,q.R)(),s=!e||t?"256px":"64px",u=n?.user_metadata?.first_name||n?.user_metadata?.full_name?.split(" ")[0]||"",[g,x]=(0,o.useState)([]),[f,b]=(0,o.useState)(""),[w,v]=(0,o.useState)(!0);(0,o.useCallback)(async e=>{if(e)try{let t=await fetch(`/api/keys?custom_config_id=${e}`,{cache:"force-cache",headers:{"Cache-Control":"max-age=300"}});t.ok&&await t.json()}catch(e){}},[]);let[k,j]=(0,o.useState)(""),[C,N]=(0,o.useState)([]),[S,T]=(0,o.useState)(!1),[A,E]=(0,o.useState)(null),[L,M]=(0,o.useState)(!0),[R,_]=(0,o.useState)(!1),[P,O]=(0,o.useState)([]),[$,I]=(0,o.useState)([]),z=(0,o.useRef)(null),K=(0,o.useRef)(null),Z=(0,o.useRef)(null),[V,U]=(0,o.useState)(!1),[Y,J]=(0,o.useState)(null),[G,X]=(0,o.useState)(null),[Q,ee]=(0,o.useState)(""),[ea,eo]=(0,o.useState)(!1),[en,es]=(0,o.useState)(null),[ei,el]=(0,o.useState)(!1),[ec,ed]=(0,o.useState)(!1),[em,eu]=(0,o.useState)(!1),[eg,eh]=(0,o.useState)(!1),[ep,ex]=(0,o.useState)(!1),ef=function(e={}){let t=function(e={}){let{enableAutoProgression:t=!0,stageDurations:r={},onStageChange:a}=e,[n,s]=(0,o.useState)("initializing"),[i,l]=(0,o.useState)(!1),[c,d]=(0,o.useState)([]),m=(0,o.useRef)(0),u=(0,o.useRef)([]);({...W,...r});let g=(0,o.useCallback)(()=>{u.current.forEach(e=>clearTimeout(e)),u.current=[]},[]),h=(0,o.useCallback)((e,t=!0)=>{let r=Date.now();if("connecting"===e){s(e),d(t=>[...t,{stage:e,timestamp:r}]),a?.(e,r);let t=setTimeout(()=>{s("routing"),d(e=>[...e,{stage:"routing",timestamp:Date.now()}]),a?.("routing",Date.now())},2e3);u.current.push(t);return}t&&g(),s(e),d(t=>[...t,{stage:e,timestamp:r}]),a?.(e,r)},[a,g]),p=(0,o.useCallback)(()=>{let e;l(!0),m.current=Date.now(),d([{stage:"initializing",timestamp:Date.now()}]),s("initializing");let t=(e,t=30)=>{let r=(Math.random()-.5)*2*(t/100*e);return Math.max(200,Math.round(e+r))},r=setTimeout(()=>{h("analyzing",!1)},e=0+t(900,35)),a=setTimeout(()=>{h("complexity_analysis",!1)},e+=t(1200,40)),o=setTimeout(()=>{h("role_classification",!1)},e+=t(1500,35)),n=setTimeout(()=>{h("preparing",!1)},e+=t(1e3,40)),i=setTimeout(()=>{h("connecting",!1)},e+=t(1200,35)),c=setTimeout(()=>{h("routing",!1)},e+=t(1500,40)),g=setTimeout(()=>{h("generating",!1)},e+=t(1200,35));u.current.push(r,a,o,n,i,c,g)},[a,h]),x=(0,o.useCallback)(()=>{g();let e=Date.now();s("typing"),d(t=>[...t,{stage:"typing",timestamp:e}]),a?.("typing",e)},[g,a]),f=(0,o.useCallback)(()=>{g(),h("complete"),l(!1)},[g,h]),b=(0,o.useCallback)(()=>{g();let e=Date.now();s("generating"),d(t=>[...t,{stage:"generating",timestamp:e}]),a?.("generating",e)},[g,a]),y=(0,o.useCallback)(e=>{},[]);return{currentStage:n,isActive:i,stageHistory:c,startProcessing:p,updateStage:h,markStreaming:x,markComplete:f,markOrchestrationStarted:b,updateOrchestrationStatus:y,reset:(0,o.useCallback)(()=>{g(),s("initializing"),l(!1),d([]),m.current=0},[g]),getProcessingDuration:(0,o.useCallback)(()=>0===m.current?0:Date.now()-m.current,[])}}(e),[r,a]=(0,o.useState)(new Set),n=(0,o.useCallback)(e=>{e.get("x-rokey-role-used"),e.get("x-rokey-routing-strategy"),e.get("x-rokey-complexity-level"),e.get("x-rokey-api-key-provider"),t.updateStage("generating")},[t]),s=(0,o.useCallback)(e=>{e.includes("[Complexity Classification]")&&!r.has("complexity")&&(t.updateStage("complexity_analysis"),a(e=>new Set([...e,"complexity"]))),e.includes("[Intelligent Role Strategy]")&&!r.has("role")&&(t.updateStage("role_classification"),a(e=>new Set([...e,"role"]))),e.includes("FIRST TOKEN:")&&!r.has("streaming")&&(t.markStreaming(),a(e=>new Set([...e,"streaming"])))},[t,r]),i=(0,o.useCallback)(()=>{t.reset(),a(new Set)},[t]);return{...t,analyzeResponseHeaders:n,analyzeStreamChunk:s,reset:i,detectedStages:Array.from(r)}}({enableAutoProgression:!0,onStageChange:void 0}),[eb,ey]=(0,o.useState)(""),ew=(e,t)=>{let r="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))r="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))r="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))r="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);r=t?`${t[1]} Specialist working`:"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?r="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(r="Analyzing and processing with specialized expertise");r&&r!==eb&&(ey(r),t.updateOrchestrationStatus(r))},ev=async()=>{if(f&&Y){T(!0),ey("Continuing synthesis automatically..."),ef.startProcessing();try{let e={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};N(t=>[...t,e]),await eH(Y.id,e);let t={custom_api_config_id:f,messages:[...C.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:L,...n?.id&&{_internal_user_id:n.id}},r=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(t),cache:"no-store"});if(r.ok){let e,t=await r.text();try{e=JSON.parse(t)}catch{e=null}if(e?.error==="synthesis_complete"){N(e=>e.slice(0,-1)),T(!1),ey(""),ef.markComplete(),j("continue"),setTimeout(()=>{eJ()},100);return}let a=new Response(t,{status:r.status,statusText:r.statusText,headers:r.headers});if(L&&a.body){let e=a.body.getReader(),t=new TextDecoder,r=Date.now().toString()+"-assistant-continue",o={id:r,role:"assistant",content:[{type:"text",text:""}]};N(e=>[...e,o]);let n="",s=!1,i=null,l=a.headers.get("X-Synthesis-Progress"),c=a.headers.get("X-Synthesis-Complete"),d=null!==l;for(d?(ef.markStreaming(),ey("")):(ef.markOrchestrationStarted(),ey("Continuing synthesis..."),i=setTimeout(()=>{s||(ef.markStreaming(),ey(""))},800));;){let{done:a,value:l}=await e.read();if(a)break;for(let e of t.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!d&&!s&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))?(s=!0,i&&(clearTimeout(i),i=null),ew(t,ef)):!d&&s&&ew(t,ef);let a=o.content[0];a.text=n,N(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n){let e={...o,content:[{type:"text",text:n}]};d&&"true"!==c&&n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eH(Y.id,e),setTimeout(()=>{ev()},1e3)):await eH(Y.id,e)}}}else throw Error(`Auto-continuation failed: ${r.status}`)}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:`Auto-continuation failed: ${t instanceof Error?t.message:"Unknown error"}`}]};N(t=>[...t,e])}finally{T(!1),ey(""),ef.markComplete()}}},{chatHistory:ek,isLoading:ej,isStale:eC,error:eN,refetch:eS,prefetch:eT,invalidateCache:eA}=(0,B.mx)({configId:f,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eE}=(0,B.l2)(),eL=(0,o.useMemo)(()=>[{id:"write-copy",title:"Write copy",description:"Create compelling marketing content",icon:"✍️",color:"bg-amber-100 text-amber-700",prompt:"Help me write compelling copy for my product landing page"},{id:"image-generation",title:"Image generation",description:"Create visual content descriptions",icon:"\uD83C\uDFA8",color:"bg-blue-100 text-blue-700",prompt:"Help me create detailed prompts for AI image generation"},{id:"create-avatar",title:"Create avatar",description:"Design character personas",icon:"\uD83D\uDC64",color:"bg-green-100 text-green-700",prompt:"Help me create a detailed character avatar for my story"},{id:"write-code",title:"Write code",description:"Generate and debug code",icon:"\uD83D\uDCBB",color:"bg-purple-100 text-purple-700",prompt:"Help me write clean, efficient code for my project"}],[]),eM=e=>new Promise((t,r)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>t(a.result),a.onerror=e=>r(e)}),eR=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let r=P.length,a=t.slice(0,10-r);a.length<t.length&&E(`You can only upload up to 10 images. ${t.length-a.length} images were not added.`);try{let e=[];for(let t of a){let r=await eM(t);e.push(r)}O(e=>[...e,...a]),I(t=>[...t,...e])}catch(e){E("Failed to process one or more images. Please try again.")}z.current&&(z.current.value="")},e_=e=>{void 0!==e?(O(t=>t.filter((t,r)=>r!==e)),I(t=>t.filter((t,r)=>r!==e))):(O([]),I([])),z.current&&(z.current.value="")},eP=(e=!1)=>{Z.current&&Z.current.scrollTo({top:Z.current.scrollHeight,behavior:e?"smooth":"auto"})},eO=(0,o.useMemo)(()=>C.length>100?C.slice(-50):C,[C]),e$=async(e,t=!1)=>{t||eo(!0);try{let r=t?C.length:0,a=Date.now(),o=await fetch(`/api/chat/messages?conversation_id=${e.id}&limit=50&offset=${r}&latest=${!t}&_cb=${a}`,{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!o.ok)throw Error("Failed to load conversation messages");let n=(await o.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""})}));t?N(e=>[...n,...e]):(N(n),Y&&Y.id===e.id||J(e)),E(null)}catch(e){E(`Failed to load conversation: ${e.message}`)}finally{t||eo(!1)}},eI=async()=>{if(!f||0===C.length)return null;try{let e=Y?.id;if(!e){let t=C[0],r="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(r=e.text.slice(0,50)+(e.text.length>50?"...":""))}let a={custom_api_config_id:f,title:r},o=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!o.ok)throw Error("Failed to create conversation");let n=await o.json();e=n.id,J(n)}for(let t of C){if(t.id.includes("-")&&t.id.length>20)continue;let r={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})}return Y||eS(!0),e}catch(e){return E(`Failed to save conversation: ${e.message}`),null}},eD=async e=>{try{if(!(await fetch(`/api/chat/conversations?id=${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete conversation");Y?.id===e&&(J(null),N([])),eS(!0)}catch(e){E(`Failed to delete conversation: ${e.message}`)}},ez=async e=>{if(!f)return null;try{let t="New Chat";if(e.content.length>0){let r=e.content.find(e=>"text"===e.type);r&&r.text&&(t=r.text.slice(0,50)+(r.text.length>50?"...":""))}let r={custom_api_config_id:f,title:t},a=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok)throw Error("Failed to create conversation");let o=await a.json();return J(o),o.id}catch(e){return E(`Failed to create conversation: ${e.message}`),null}},eH=async(e,t)=>{try{let r={conversation_id:e,role:t.role,content:t.content},a=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok)throw Error("Failed to save message");return await a.json()}catch(e){}},eF=e=>{j(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},eB=async()=>{C.length>0&&await eI(),N([]),J(null),j(""),E(null),e_(),ef.reset()},eW=(0,o.useCallback)(async e=>{if(e===f)return;C.length>0&&await eB(),b(e);let t=g.find(t=>t.id===e);t&&t.name},[f,C.length,eB,b,g]),eq=async e=>{J(e),N([]),j(""),E(null),e_();let t=(async()=>{if(C.length>0&&!Y)try{await eI()}catch(e){}})();try{await e$(e)}catch(e){E(`Failed to load conversation: ${e.message}`)}await t},eK=(e,t)=>{X(e),ee(t)},eZ=()=>{X(null),ee("")},eV=async()=>{if(!G||!Q.trim()||!f)return;let e=C.findIndex(e=>e.id===G);if(-1===e)return;let t=[...C];t[e]={...t[e],content:[{type:"text",text:Q.trim()}]};let r=t.slice(0,e+1);if(N(r),X(null),ee(""),Y)try{if(C.slice(e+1).length>0){let t=C[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch(`/api/chat/messages?conversation_id=${Y.id}&limit=1&latest=false`);if(e.ok){let r=(await e.json()).find(e=>e.id===t.id);if(r){let e=new Date(r.created_at).getTime(),t=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:Y.id,after_timestamp:e})});t.ok&&await t.json()}}}else{let e=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:Y.id,after_timestamp:e})});r.ok&&await r.json()}}let t=r[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch(`/api/chat/messages?id=${t.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t.content})});if(e.ok)await e.json();else{let t=await e.text();throw Error(`Failed to update message: ${t}`)}}else await eH(Y.id,t);eS(!0)}catch(e){E(`Failed to update conversation: ${e.message}`)}await eU(r)},eU=async e=>{if(!f||0===e.length)return;T(!0),E(null),ef.startProcessing();let t={custom_api_config_id:f,messages:e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:L,...n?.id&&{_internal_user_id:n.id}};try{ef.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(t),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`API Error: ${e.statusText} (Status: ${e.status})`)}if(ef.analyzeResponseHeaders(e.headers),setTimeout(()=>{L&&ef.markStreaming()},400),L&&e.body){let t=e.body.getReader(),r=new TextDecoder,a=Date.now().toString()+"-assistant",o={id:a,role:"assistant",content:[{type:"text",text:""}]};N(e=>[...e,o]);let n="",s=!1,i=null,l="";for(i=setTimeout(()=>{s||ef.markStreaming()},400);;){let{done:e,value:c}=await t.read();if(e)break;for(let e of r.decode(c,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){s=!0,l=e.data.message,i&&(clearTimeout(i),i=null),ef.markOrchestrationStarted(),ey(l);return}if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!s&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(s=!0,i&&(clearTimeout(i),i=null),ef.markOrchestrationStarted()),s&&!l&&ew(t,ef);let r=o.content[0];r.text=n,N(e=>e.map(e=>e.id===a?{...e,content:[r]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n&&Y){let e={...o,content:[{type:"text",text:n}]};n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||n.includes("*The response will continue automatically in a new message...*")?(await eH(Y.id,e),setTimeout(()=>{ev()},2e3)):await eH(Y.id,e)}}else{let t=await e.json(),r="Could not parse assistant's response.";t.choices?.[0]?.message?.content?r=t.choices[0].message.content:t.content?.[0]?.text?r=t.content[0].text:"string"==typeof t.text&&(r=t.text);let a={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:r}]};N(e=>[...e,a]),Y&&await eH(Y.id,a)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};N(t=>[...t,e]),E(t.message)}finally{T(!1),ef.markComplete()}},eY=async(e,t)=>{if(!f||e<0||e>=C.length||"assistant"!==C[e].role)return;T(!0),E(null),ey(""),ef.startProcessing();let r=C.slice(0,e);if(N(r),Y)try{if(C.slice(e).length>0){let t=C[e],r=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:Y.id,from_timestamp:r})});a.ok&&await a.json()}eS(!0)}catch(e){}let a={custom_api_config_id:f,messages:r.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:L,...t&&{specific_api_key_id:t},...n?.id&&{_internal_user_id:n.id}};try{ef.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(a),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`HTTP ${e.status}: ${e.statusText}`)}ef.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),r=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===r&&(es(t),el(!0),ed(!1)),setTimeout(()=>{L&&ef.markStreaming()},400),L&&e.body){let t=e.body.getReader(),r=new TextDecoder,a="",o={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};N(e=>[...e,o]);try{for(;;){let{done:e,value:n}=await t.read();if(e)break;for(let e of r.decode(n,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){let t=e.choices[0].delta.content;a+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(ef.markOrchestrationStarted(),ew(t,ef)):eb&&ew(t,ef),N(e=>e.map(e=>e.id===o.id?{...e,content:[{type:"text",text:a}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(a&&Y){let e={...o,content:[{type:"text",text:a}]};await eH(Y.id,e)}}else{let t=await e.json(),r="";t.choices&&t.choices.length>0&&t.choices[0].message?r=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(r=t.content[0].text);let a={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:r}]};N(e=>[...e,a]),Y&&await eH(Y.id,a)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};N(t=>[...t,e]),E(t.message),Y&&await eH(Y.id,e)}finally{T(!1),ef.markComplete()}},eJ=(0,o.useCallback)(async e=>{if(e&&e.preventDefault(),!k.trim()&&0===P.length||!f)return;if("continue"===k.trim().toLowerCase()&&C.length>0){j(""),await ev();return}T(!0),E(null),ey(""),ef.startProcessing(),performance.now();let t=k.trim(),r=[...P],a=[...$];j(""),e_();let o=[],s=[];if(t&&(o.push({type:"text",text:t}),s.push({type:"text",text:t})),r.length>0)try{for(let e=0;e<r.length;e++){let t=r[e],n=a[e],i=await eM(t);o.push({type:"image_url",image_url:{url:n}}),s.push({type:"image_url",image_url:{url:i}})}}catch(e){E("Failed to process one or more images. Please try again."),T(!1),j(t),O(r),I(a);return}let i={id:Date.now().toString(),role:"user",content:o};N(e=>[...e,i]);let l=Y?.id,c=Promise.resolve(l||null);Promise.resolve(),l||Y||(c=ez(i)),c.then(async e=>{e&&await eH(e,i)}).catch(e=>{});let d={custom_api_config_id:f,messages:[...C.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),{role:"user",content:1===s.length&&"text"===s[0].type?s[0].text:s}],stream:L,...n?.id&&{_internal_user_id:n.id}};try{ef.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(d),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`API Error: ${e.statusText} (Status: ${e.status})`)}ef.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),r=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===r&&(es(t),el(!0),ed(!1)),L&&e.body){let t=e.body.getReader(),r=new TextDecoder,a=Date.now().toString()+"-assistant",o={id:a,role:"assistant",content:[{type:"text",text:""}]};N(e=>[...e,o]);let n="",s=!1,i=null,l="";for(i=setTimeout(()=>{s||ef.markStreaming()},400);;){let{done:e,value:c}=await t.read();if(e)break;for(let e of r.decode(c,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){s=!0,l=e.data.message,i&&(clearTimeout(i),i=null),ef.markOrchestrationStarted(),ey(l);continue}if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!s&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(s=!0,i&&(clearTimeout(i),i=null),ef.markOrchestrationStarted()),s&&!l&&ew(t,ef);let r=o.content[0];r.text=n,N(e=>e.map(e=>e.id===a?{...e,content:[r]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n){let t={...o,content:[{type:"text",text:n}]},r=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||n.includes("*The response will continue automatically in a new message...*")?(c.then(async e=>{e&&await eH(e,t)}),setTimeout(()=>{ev()},null!==r?1e3:2e3)):c.then(async e=>{e&&await eH(e,t)})}}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};N(t=>[...t,e]),E(t.message),c.then(async t=>{t&&await eH(t,e)}).catch(e=>{})}finally{T(!1),ef.markComplete(),function(e){if(!(e.length<2)){for(let t=1;t<e.length;t++){let r=e[t],a=e[t-1];r.timestamp,a.timestamp}e[e.length-1].timestamp,e[0].timestamp}}(ef.stageHistory),performance.now(),c.then(async e=>{e&&!Y&&eS(!0)}).catch(e=>{})}},[k,P,f,S,Y,C,ef,L,eb,N,E,T,J,j,O,I,eS]);return(0,a.jsxs)("div",{className:"min-h-screen bg-[#faf8f5] flex",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:s,marginRight:em&&!eg?"50%":V?"0px":"320px"},children:[(0,a.jsx)("div",{className:"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out",style:{left:s,right:em&&!eg?"50%":V?"0px":"320px"},children:(0,a.jsx)("div",{className:"px-6 py-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connected"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Not Connected"})]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{value:f,onChange:e=>eW(e.target.value),disabled:0===g.length,className:"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,a.jsx)("option",{value:"",children:"Select Router"}),g.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Streaming"}),(0,a.jsx)("button",{onClick:()=>M(!L),className:`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm ${L?"bg-orange-500 shadow-orange-200":"bg-gray-300"}`,children:(0,a.jsx)("span",{className:`inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${L?"translate-x-6":"translate-x-1"}`})})]})]})})}),(0,a.jsx)("div",{className:"flex-1 flex flex-col pt-0 pb-32",children:0!==C.length||Y?(0,a.jsxs)("div",{className:`flex-1 relative ${em&&!eg?"overflow-visible":"overflow-hidden"}`,children:[(0,a.jsx)("div",{className:`h-full flex ${em&&!eg?"justify-start":"justify-center"}`,children:(0,a.jsx)("div",{ref:Z,className:`w-full h-full overflow-y-auto px-6 transition-all duration-300 ${em&&!eg?"max-w-2xl -ml-32":"max-w-4xl"}`,onScroll:e=>{let t=e.currentTarget;_(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&C.length>0)},children:(0,a.jsxs)("div",{className:"space-y-6 py-0",children:[Y&&C.length>=50&&(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsx)("button",{onClick:()=>e$(Y,!0),disabled:ej,className:"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50",children:ej?"Loading...":"Load Earlier Messages"})}),ea&&0===C.length&&(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-start",children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,a.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),C.length>100&&(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-500 bg-gray-50 rounded-lg px-4 py-2 inline-block",children:["Showing last 50 of ",C.length," messages for better performance"]})}),eO.map((e,t)=>(0,a.jsxs)("div",{className:`flex ${"user"===e.role?"justify-end":"justify-start"} group ${em&&!eg?"-ml-96":""} ${"assistant"===e.role&&em&&!eg?"ml-8":""} ${0===t?"pt-3":""}`,children:["assistant"===e.role&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsxs)("div",{className:`${"user"===e.role?em&&!eg?"max-w-[60%]":"max-w-[50%]":em&&!eg?"max-w-[85%]":"max-w-[75%]"} relative ${"user"===e.role?"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm":"assistant"===e.role?"text-gray-900":"system"===e.role?"bg-amber-50 text-amber-800 rounded-xl border border-amber-200":"bg-red-50 text-red-800 rounded-xl border border-red-200"} px-4 py-3 transition-all duration-300`,children:["user"===e.role&&(0,a.jsxs)("div",{className:"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,a.jsx)(p.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer"}),(0,a.jsx)("button",{onClick:()=>eK(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,a.jsx)(m,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,a.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,a.jsx)(p.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&f&&(0,a.jsx)(y,{configId:f,onRetry:e=>eY(t,e),disabled:S})]}),(0,a.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&G===e.id?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:Q,onChange:e=>ee(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:eV,disabled:!Q.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,a.jsx)(i,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save & Continue"})]}),(0,a.jsxs)("button",{onClick:eZ,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Cancel"})]})]}),(0,a.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,r)=>{if("text"===t.type)if("assistant"===e.role)return(0,a.jsx)(h,{content:t.text,className:"text-sm"},r);else return(0,a.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-sm",children:t.text},r);return"image_url"===t.type?(0,a.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},r):null})})]}),"user"===e.role&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]},e.id)),ei&&en&&eg&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(H,{orchestrationComplete:ec,onMaximize:()=>{ex(!0),setTimeout(()=>ex(!1),100)},isCanvasOpen:em,isCanvasMinimized:eg})}),S&&(0,a.jsxs)("div",{className:"flex justify-start group",children:[(!eb||"typing"===ef.currentStage)&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)(D,{currentStage:ef.currentStage,isStreaming:L&&"typing"===ef.currentStage,orchestrationStatus:eb,onStageChange:e=>{}})]}),ei&&en&&(0,a.jsx)(o.Suspense,{fallback:(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"}),(0,a.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading orchestration canvas..."})]}),children:(0,a.jsx)(et,{executionId:en,onCanvasStateChange:(e,t)=>{eu(e),eh(t),e&&!t&&U(!0)},forceMaximize:ep,onComplete:e=>{if(en?.startsWith("test-execution-id"))return void ed(!0);ed(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};N(e=>[...e,t]),el(!1),es(null),ed(!1),Y?.id&&eH(Y.id,t).catch(e=>{})},onError:e=>{en?.startsWith("test-execution-id")||(E(`Orchestration error: ${e}`),el(!1),es(null))}})}),(0,a.jsx)("div",{ref:K})]})})}),R&&(0,a.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,a.jsx)("button",{onClick:()=>eP(!0),className:"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 overflow-hidden",children:(0,a.jsx)("div",{className:`w-full mx-auto transition-all duration-300 ${em&&!eg?"max-w-2xl -ml-32":"max-w-4xl"}`,children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["Welcome",u?` ${u}`:""," to RouKey"]}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-md mx-auto",children:"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:eL.map(e=>(0,a.jsxs)("button",{onClick:()=>eF(e.prompt),disabled:!f,className:`group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ${!f?"cursor-not-allowed":"cursor-pointer hover:scale-[1.02]"}`,children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center text-xl ${e.color} group-hover:scale-110 transition-transform duration-200`,children:e.icon}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),(0,a.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,a.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out",style:{left:s,right:em&&!eg?"50%":V?"0px":"320px"},children:(0,a.jsx)("div",{className:"px-6 pt-3 pb-2 flex justify-center",children:(0,a.jsxs)("div",{className:`w-full transition-all duration-300 ${em&&!eg?"max-w-2xl":"max-w-4xl"}`,children:[A&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-red-800 text-sm font-medium",children:A})]})}),!1,(0,a.jsxs)("form",{onSubmit:eJ,children:[$.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[$.length," image",$.length>1?"s":""," attached"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>e_(),className:"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:$.map((e,t)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,a.jsx)("img",{src:e,alt:`Preview ${t+1}`,className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,a.jsx)("button",{type:"button",onClick:()=>e_(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":`Remove image ${t+1}`,children:(0,a.jsx)(l,{className:"w-3.5 h-3.5"})})]}),(0,a.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,a.jsx)("div",{className:"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300",children:(0,a.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eR,ref:z,className:"hidden",id:"imageUpload"}),(0,a.jsxs)("button",{type:"button",onClick:()=>z.current?.click(),disabled:P.length>=10,className:`relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ${P.length>=10?"text-gray-300 cursor-not-allowed":"text-gray-400 hover:text-orange-500 hover:bg-orange-50"}`,"aria-label":P.length>=10?"Maximum 10 images reached":"Attach images",title:P.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,a.jsx)(c,{className:"w-5 h-5"}),P.length>0&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:P.length})]}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("textarea",{value:k,onChange:e=>j(e.target.value),placeholder:f?"Type a message...":"Select a router first",disabled:!f||S,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(k.trim()||P.length>0)&&f&&!S&&eJ())},style:{minHeight:"24px",maxHeight:"120px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,a.jsx)("button",{type:"submit",disabled:!f||S||!k.trim()&&0===P.length,className:"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0","aria-label":"Send message",title:"Send message",children:S?(0,a.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,a.jsx)(d,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,a.jsx)("div",{className:`fixed top-0 right-0 h-full bg-white shadow-xl transition-all duration-300 ease-in-out z-30 ${V?"w-0 overflow-hidden":"w-80"}`,style:{transform:V?"translateX(100%)":"translateX(0)",opacity:+!V},children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200/50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"History"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[ek.length," conversations"]})]})]}),(0,a.jsx)("button",{onClick:()=>U(!V),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsx)("div",{className:"p-4 border-b border-gray-200/50",children:(0,a.jsxs)("button",{onClick:eB,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,a.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:ej?(0,a.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,a.jsxs)("div",{className:"p-3 rounded-xl border border-gray-100 animate-pulse",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded mb-2"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"})]},t))}):0===ek.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No conversations yet"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Start chatting to see your history"})]}):(0,a.jsx)(a.Fragment,{children:ek.map(e=>(0,a.jsx)(er,{chat:e,currentConversation:Y,onLoadChat:eq,onDeleteChat:eD},e.id))})}),eC&&(0,a.jsx)("div",{className:"px-4 py-2 bg-orange-50 border-t border-orange-100",children:(0,a.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,a.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),eN&&(0,a.jsx)("div",{className:"px-4 py-2 bg-red-50 border-t border-red-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,a.jsx)("span",{children:"Failed to load history"}),(0,a.jsx)("button",{onClick:()=>eS(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,a.jsx)("div",{className:`fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ${V?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"}`,children:(0,a.jsx)("button",{onClick:()=>U(!1),className:"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105","aria-label":"Show history sidebar",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}er.displayName="ChatHistoryItem"},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54759:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),o=r(48088),n=r(88170),s=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["playground",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21590)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,45807)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/playground/page",pathname:"/playground",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58089:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},62392:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64364:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},71178:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},72132:(e,t,r)=>{Promise.resolve().then(r.bind(r,35291))},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89530:(e,t,r)=>{Promise.resolve().then(r.bind(r,21590))},90692:(e,t,r)=>{Promise.resolve().then(r.bind(r,47417))},91645:e=>{"use strict";e.exports=require("net")},93635:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(43210);let o=a.forwardRef(function({title:e,titleId:t,...r},o){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7482,4912],()=>r(54759));module.exports=a})();