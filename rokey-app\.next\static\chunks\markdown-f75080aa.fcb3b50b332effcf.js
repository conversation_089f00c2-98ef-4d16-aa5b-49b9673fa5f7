(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3285],{3862:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=48&&t<=57}},14805:(e,t,n)=>{var r=function(e){var t=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,n=0,r={},a={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof i?new i(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(t,n){var r,i;switch(n=n||{},a.util.type(t)){case"Object":if(n[i=a.util.objId(t)])return n[i];for(var o in r={},n[i]=r,t)t.hasOwnProperty(o)&&(r[o]=e(t[o],n));return r;case"Array":if(n[i=a.util.objId(t)])return n[i];return r=[],n[i]=r,t.forEach(function(t,a){r[a]=e(t,n)}),r;default:return t}},getLanguage:function(e){for(;e;){var n=t.exec(e.className);if(n)return n[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,n){e.className=e.className.replace(RegExp(t,"gi"),""),e.classList.add("language-"+n)},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw Error()}catch(r){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(r.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var n in t)if(t[n].src==e)return t[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var a=e.classList;if(a.contains(t))return!0;if(a.contains(r))return!1;e=e.parentElement}return!!n}},languages:{plain:r,plaintext:r,text:r,txt:r,extend:function(e,t){var n=a.util.clone(a.languages[e]);for(var r in t)n[r]=t[r];return n},insertBefore:function(e,t,n,r){var i=(r=r||a.languages)[e],o={};for(var l in i)if(i.hasOwnProperty(l)){if(l==t)for(var c in n)n.hasOwnProperty(c)&&(o[c]=n[c]);n.hasOwnProperty(l)||(o[l]=i[l])}var s=r[e];return r[e]=o,a.languages.DFS(a.languages,function(t,n){n===s&&t!=e&&(this[t]=o)}),o},DFS:function e(t,n,r,i){i=i||{};var o=a.util.objId;for(var l in t)if(t.hasOwnProperty(l)){n.call(t,l,t[l],r||l);var c=t[l],s=a.util.type(c);"Object"!==s||i[o(c)]?"Array"!==s||i[o(c)]||(i[o(c)]=!0,e(c,n,l,i)):(i[o(c)]=!0,e(c,n,null,i))}}},plugins:{},highlightAll:function(e,t){a.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),a.hooks.run("before-all-elements-highlight",r);for(var i,o=0;i=r.elements[o++];)a.highlightElement(i,!0===t,r.callback)},highlightElement:function(t,n,r){var i=a.util.getLanguage(t),o=a.languages[i];a.util.setLanguage(t,i);var l=t.parentElement;l&&"pre"===l.nodeName.toLowerCase()&&a.util.setLanguage(l,i);var c=t.textContent,s={element:t,language:i,grammar:o,code:c};function u(e){s.highlightedCode=e,a.hooks.run("before-insert",s),s.element.innerHTML=s.highlightedCode,a.hooks.run("after-highlight",s),a.hooks.run("complete",s),r&&r.call(s.element)}if(a.hooks.run("before-sanity-check",s),(l=s.element.parentElement)&&"pre"===l.nodeName.toLowerCase()&&!l.hasAttribute("tabindex")&&l.setAttribute("tabindex","0"),!s.code){a.hooks.run("complete",s),r&&r.call(s.element);return}if(a.hooks.run("before-highlight",s),!s.grammar)return void u(a.util.encode(s.code));if(n&&e.Worker){var g=new Worker(a.filename);g.onmessage=function(e){u(e.data)},g.postMessage(JSON.stringify({language:s.language,code:s.code,immediateClose:!0}))}else u(a.highlight(s.code,s.grammar,s.language))},highlight:function(e,t,n){var r={code:e,grammar:t,language:n};if(a.hooks.run("before-tokenize",r),!r.grammar)throw Error('The language "'+r.language+'" has no grammar.');return r.tokens=a.tokenize(r.code,r.grammar),a.hooks.run("after-tokenize",r),i.stringify(a.util.encode(r.tokens),r.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}var s=new l;return c(s,s.head,e),function e(t,n,r,l,s,u){for(var g in r)if(r.hasOwnProperty(g)&&r[g]){var f=r[g];f=Array.isArray(f)?f:[f];for(var h=0;h<f.length;++h){if(u&&u.cause==g+","+h)return;var d=f[h],m=d.inside,p=!!d.lookbehind,v=!!d.greedy,x=d.alias;if(v&&!d.pattern.global){var y=d.pattern.toString().match(/[imsuy]*$/)[0];d.pattern=RegExp(d.pattern.source,y+"g")}for(var b=d.pattern||d,k=l.next,A=s;k!==n.tail&&(!u||!(A>=u.reach));A+=k.value.length,k=k.next){var w,C=k.value;if(n.length>t.length)return;if(!(C instanceof i)){var E=1;if(v){if(!(w=o(b,A,t,p))||w.index>=t.length)break;var O=w.index,N=w.index+w[0].length,L=A;for(L+=k.value.length;O>=L;)L+=(k=k.next).value.length;if(L-=k.value.length,A=L,k.value instanceof i)continue;for(var S=k;S!==n.tail&&(L<N||"string"==typeof S.value);S=S.next)E++,L+=S.value.length;E--,C=t.slice(A,L),w.index-=A}else if(!(w=o(b,0,C,p)))continue;var O=w.index,P=w[0],T=C.slice(0,O),M=C.slice(O+P.length),j=A+C.length;u&&j>u.reach&&(u.reach=j);var I=k.prev;if(T&&(I=c(n,I,T),A+=T.length),function(e,t,n){for(var r=t.next,a=0;a<n&&r!==e.tail;a++)r=r.next;t.next=r,r.prev=t,e.length-=a}(n,I,E),k=c(n,I,new i(g,m?a.tokenize(P,m):P,x,P)),M&&c(n,k,M),E>1){var _={cause:g+","+h,reach:j};e(t,n,r,k.prev,A,_),u&&_.reach>u.reach&&(u.reach=_.reach)}}}}}}(e,s,t,s.head,0),function(e){for(var t=[],n=e.head.next;n!==e.tail;)t.push(n.value),n=n.next;return t}(s)},hooks:{all:{},add:function(e,t){var n=a.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=a.hooks.all[e];if(n&&n.length)for(var r,i=0;r=n[i++];)r(t)}},Token:i};function i(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function o(e,t,n,r){e.lastIndex=t;var a=e.exec(n);if(a&&r&&a[1]){var i=a[1].length;a.index+=i,a[0]=a[0].slice(i)}return a}function l(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function c(e,t,n){var r=t.next,a={value:n,prev:t,next:r};return t.next=a,r.prev=a,e.length++,a}if(e.Prism=a,i.stringify=function e(t,n){if("string"==typeof t)return t;if(Array.isArray(t)){var r="";return t.forEach(function(t){r+=e(t,n)}),r}var i={type:t.type,content:e(t.content,n),tag:"span",classes:["token",t.type],attributes:{},language:n},o=t.alias;o&&(Array.isArray(o)?Array.prototype.push.apply(i.classes,o):i.classes.push(o)),a.hooks.run("wrap",i);var l="";for(var c in i.attributes)l+=" "+c+'="'+(i.attributes[c]||"").replace(/"/g,"&quot;")+'"';return"<"+i.tag+' class="'+i.classes.join(" ")+'"'+l+">"+i.content+"</"+i.tag+">"},!e.document)return e.addEventListener&&(a.disableWorkerMessageHandler||e.addEventListener("message",function(t){var n=JSON.parse(t.data),r=n.language,i=n.code,o=n.immediateClose;e.postMessage(a.highlight(i,a.languages[r],r)),o&&e.close()},!1)),a;var s=a.util.currentScript();function u(){a.manual||a.highlightAll()}if(s&&(a.filename=s.src,s.hasAttribute("data-manual")&&(a.manual=!0)),!a.manual){var g=document.readyState;"loading"===g||"interactive"===g&&s&&s.defer?document.addEventListener("DOMContentLoaded",u):window.requestAnimationFrame?window.requestAnimationFrame(u):window.setTimeout(u,16)}return a}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=r),void 0!==n.g&&(n.g.Prism=r)},25298:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=102||t>=65&&t<=70||t>=48&&t<=57}},69510:e=>{"use strict";e.exports=JSON.parse('{"AElig":"\xc6","AMP":"&","Aacute":"\xc1","Acirc":"\xc2","Agrave":"\xc0","Aring":"\xc5","Atilde":"\xc3","Auml":"\xc4","COPY":"\xa9","Ccedil":"\xc7","ETH":"\xd0","Eacute":"\xc9","Ecirc":"\xca","Egrave":"\xc8","Euml":"\xcb","GT":">","Iacute":"\xcd","Icirc":"\xce","Igrave":"\xcc","Iuml":"\xcf","LT":"<","Ntilde":"\xd1","Oacute":"\xd3","Ocirc":"\xd4","Ograve":"\xd2","Oslash":"\xd8","Otilde":"\xd5","Ouml":"\xd6","QUOT":"\\"","REG":"\xae","THORN":"\xde","Uacute":"\xda","Ucirc":"\xdb","Ugrave":"\xd9","Uuml":"\xdc","Yacute":"\xdd","aacute":"\xe1","acirc":"\xe2","acute":"\xb4","aelig":"\xe6","agrave":"\xe0","amp":"&","aring":"\xe5","atilde":"\xe3","auml":"\xe4","brvbar":"\xa6","ccedil":"\xe7","cedil":"\xb8","cent":"\xa2","copy":"\xa9","curren":"\xa4","deg":"\xb0","divide":"\xf7","eacute":"\xe9","ecirc":"\xea","egrave":"\xe8","eth":"\xf0","euml":"\xeb","frac12":"\xbd","frac14":"\xbc","frac34":"\xbe","gt":">","iacute":"\xed","icirc":"\xee","iexcl":"\xa1","igrave":"\xec","iquest":"\xbf","iuml":"\xef","laquo":"\xab","lt":"<","macr":"\xaf","micro":"\xb5","middot":"\xb7","nbsp":"\xa0","not":"\xac","ntilde":"\xf1","oacute":"\xf3","ocirc":"\xf4","ograve":"\xf2","ordf":"\xaa","ordm":"\xba","oslash":"\xf8","otilde":"\xf5","ouml":"\xf6","para":"\xb6","plusmn":"\xb1","pound":"\xa3","quot":"\\"","raquo":"\xbb","reg":"\xae","sect":"\xa7","shy":"\xad","sup1":"\xb9","sup2":"\xb2","sup3":"\xb3","szlig":"\xdf","thorn":"\xfe","times":"\xd7","uacute":"\xfa","ucirc":"\xfb","ugrave":"\xf9","uml":"\xa8","uuml":"\xfc","yacute":"\xfd","yen":"\xa5","yuml":"\xff"}')},70765:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(91277),a=n(47525);let i={};function o(e){let t=e||i,n=this.data(),o=n.micromarkExtensions||(n.micromarkExtensions=[]),l=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),c=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);o.push((0,a.T)(t)),l.push((0,r.C)()),c.push((0,r.H)(t))}},71280:e=>{"use strict";e.exports=JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}')},71707:e=>{"use strict";e.exports=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=122||t>=65&&t<=90}},76603:(e,t,n)=>{"use strict";var r=n(71707),a=n(3862);e.exports=function(e){return r(e)||a(e)}},76917:(e,t,n)=>{"use strict";var r=n(69510),a=n(71280),i=n(3862),o=n(25298),l=n(76603),c=n(99821);e.exports=function(e,t){var n,i,o={};for(i in t||(t={}),f)n=t[i],o[i]=null==n?f[i]:n;return(o.position.indent||o.position.start)&&(o.indent=o.position.indent||[],o.position=o.position.start),function(e,t){var n,i,o,f,y,b,k,A,w,C,E,O,N,L,S,P,T,M,j,I,_,q=t.additional,H=t.nonTerminated,U=t.text,W=t.reference,z=t.warning,F=t.textContext,G=t.referenceContext,J=t.warningContext,R=t.position,D=t.indent||[],$=e.length,B=0,Y=-1,Q=R.column||1,K=R.line||1,V="",X=[];for("string"==typeof q&&(q=q.charCodeAt(0)),M=Z(),C=z?function(e,t){var n=Z();n.column+=t,n.offset+=t,z.call(J,x[e],n,e)}:g,B--,$++;++B<$;)if(10===k&&(Q=D[Y]||1),38===(k=e.charCodeAt(B))){if(9===(w=e.charCodeAt(B+1))||10===w||12===w||32===w||38===w||60===w||w!=w||q&&w===q){V+=u(k),Q++;continue}for(L=S=B+1,_=S,35===w?(_=++L,88===(w=e.charCodeAt(_))||120===w?(P=d,_=++L):P=m):P=h,o="",N="",b="",T=v[P],_--;++_<$&&T(w=e.charCodeAt(_));)b+=u(w),P===h&&s.call(r,b)&&(o=b,N=r[b]);if((y=59===e.charCodeAt(_))&&(_++,(f=P===h&&c(b))&&(o=b,N=f)),I=1+_-S,y||H){b?P===h?(y&&!N?C(5,1):(o!==b&&(I=1+(_=L+o.length)-L,y=!1),y||(E=o?1:3,t.attribute?61===(w=e.charCodeAt(_))?(C(E,I),N=null):l(w)?N=null:C(E,I):C(E,I))),A=N):(y||C(2,I),(n=A=parseInt(b,p[P]))>=55296&&n<=57343||n>1114111?(C(7,I),A=u(65533)):A in a?(C(6,I),A=a[A]):(O="",((i=A)>=1&&i<=8||11===i||i>=13&&i<=31||i>=127&&i<=159||i>=64976&&i<=65007||(65535&i)==65535||(65535&i)==65534)&&C(6,I),A>65535&&(A-=65536,O+=u(A>>>10|55296),A=56320|1023&A),A=O+u(A))):P!==h&&C(4,I)}A?(ee(),M=Z(),B=_-1,Q+=_-S+1,X.push(A),j=Z(),j.offset++,W&&W.call(G,A,{start:M,end:j},e.slice(S-1,_)),M=j):(b=e.slice(S-1,_),V+=b,Q+=b.length,B=_-1)}else 10===k&&(K++,Y++,Q=0),k==k?(V+=u(k),Q++):ee();return X.join("");function Z(){return{line:K,column:Q,offset:B+(R.offset||0)}}function ee(){V&&(X.push(V),U&&U.call(F,V,{start:M,end:Z()}),V="")}}(e,o)};var s={}.hasOwnProperty,u=String.fromCharCode,g=Function.prototype,f={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},h="named",d="hexadecimal",m="decimal",p={};p[d]=16,p[m]=10;var v={};v[h]=l,v[m]=i,v[d]=o;var x={};x[1]="Named character references must be terminated by a semicolon",x[2]="Numeric character references must be terminated by a semicolon",x[3]="Named character references cannot be empty",x[4]="Numeric character references cannot be empty",x[5]="Named character references must be known",x[6]="Numeric character references cannot be disallowed",x[7]="Numeric character references cannot be outside the permissible Unicode range"},99821:e=>{"use strict";var t;e.exports=function(e){var n,r="&"+e+";";return(t=t||document.createElement("i")).innerHTML=r,(59!==(n=t.textContent).charCodeAt(n.length-1)||"semi"===e)&&n!==r&&n}}}]);