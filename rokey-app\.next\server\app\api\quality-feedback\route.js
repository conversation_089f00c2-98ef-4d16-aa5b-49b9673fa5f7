(()=>{var e={};e.id=407,e.ids=[407,1489],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>o});var r=s(34386),i=s(44999);async function o(){let e=await (0,i.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{POST:()=>l});var i=s(96559),o=s(48088),a=s(37719),n=s(32190),u=s(2507),c=s(45697);let p=c.z.object({custom_api_config_id:c.z.string().uuid(),api_key_id:c.z.string().uuid(),model_used:c.z.string(),provider:c.z.string(),prompt_text:c.z.string().max(2e3),response_text:c.z.string().max(1e4),user_rating:c.z.number().int().min(1).max(5),response_time_ms:c.z.number().int().positive().optional(),tokens_prompt:c.z.number().int().positive().optional(),tokens_completion:c.z.number().int().positive().optional(),cost_usd:c.z.number().positive().optional(),user_copied_response:c.z.boolean().optional().default(!1),user_regenerated:c.z.boolean().optional().default(!1),user_asked_followup:c.z.boolean().optional().default(!1),conversation_continued:c.z.boolean().optional().default(!1),routing_strategy:c.z.string().optional().default("unknown"),was_ab_test:c.z.boolean().optional().default(!1),ab_test_group:c.z.enum(["control","test"]).optional()});async function l(e){try{var t;let s,r=(0,u.Q)(e),{data:{user:i},error:o}=await r.auth.getUser();if(o||!i)return n.NextResponse.json({error:"Unauthorized"},{status:401});let a=await e.json(),c=p.parse(a),l=2*c.user_rating;c.user_copied_response&&(l+=.5),c.conversation_continued&&(l+=.5),c.user_regenerated&&(l-=1),c.user_asked_followup&&(l-=.5),l=Math.max(1,Math.min(10,l));let m=null;c.cost_usd&&c.cost_usd>0&&(m=c.cost_usd/l);let g=function(e){let t=e.toLowerCase();return t.includes("code")||t.includes("function")||t.includes("programming")||t.includes("debug")||t.includes("algorithm")?"coding":t.includes("write")||t.includes("essay")||t.includes("article")||t.includes("story")||t.includes("blog")?"writing":t.includes("analyze")||t.includes("analysis")||t.includes("compare")||t.includes("evaluate")||t.includes("research")?"analysis":t.includes("explain")||t.includes("how")||t.includes("what")||t.includes("why")||t.includes("define")?"explanation":t.includes("solve")||t.includes("calculate")||t.includes("math")||t.includes("equation")||t.includes("problem")?"reasoning":"chat"}(c.prompt_text),h=(t=c.prompt_text,s=1,t.length>500&&(s+=1),t.length>1e3&&(s+=1),s+=Math.min(["analyze","compare","evaluate","synthesize","create","design","algorithm","optimization","architecture","framework","system","research","comprehensive","detailed","thorough","complex"].filter(e=>t.toLowerCase().includes(e)).length,2),(t.match(/\?/g)||[]).length>1&&(s+=1),(t.includes("1.")||t.includes("•")||t.includes("-"))&&(s+=1),Math.min(s,5)),{data:f,error:y}=await r.from("routing_quality_metrics").insert([{user_id:i.id,custom_api_config_id:c.custom_api_config_id,api_key_id:c.api_key_id,model_used:c.model_used,provider:c.provider,prompt_text:c.prompt_text,prompt_complexity_level:h,task_category:g,quality_score:l,user_rating:c.user_rating,response_length:c.response_text.length,response_time_ms:c.response_time_ms,tokens_prompt:c.tokens_prompt,tokens_completion:c.tokens_completion,cost_usd:c.cost_usd,cost_per_quality_point:m,user_copied_response:c.user_copied_response,user_regenerated:c.user_regenerated,user_asked_followup:c.user_asked_followup,conversation_continued:c.conversation_continued,routing_strategy:c.routing_strategy,was_ab_test:c.was_ab_test,ab_test_group:c.ab_test_group}]).select().single();if(y)return n.NextResponse.json({error:"Failed to save feedback"},{status:500});return await _(i.id,c.custom_api_config_id,r),c.was_ab_test&&c.ab_test_group&&await d(i.id,c.custom_api_config_id,c.ab_test_group,l,c.cost_usd,r),n.NextResponse.json({success:!0,message:"Quality feedback recorded successfully",qualityScore:l,taskCategory:g,complexityLevel:h})}catch(e){if(e instanceof c.z.ZodError)return n.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}async function _(e,t,s){try{let{data:r}=await s.from("cost_optimization_profiles").select("*").eq("user_id",e).eq("custom_api_config_id",t).single(),i=(r?.learning_phase_requests||0)+1,o=i>=50;r?await s.from("cost_optimization_profiles").update({learning_phase_requests:i,learning_phase_completed:o,total_requests:(r.total_requests||0)+1}).eq("id",r.id):await s.from("cost_optimization_profiles").insert([{user_id:e,custom_api_config_id:t,learning_phase_requests:i,learning_phase_completed:o,total_requests:1}])}catch(e){}}async function d(e,t,s,r,i,o){try{let{data:a}=await o.from("ab_test_assignments").select("*").eq("user_id",e).eq("custom_api_config_id",t).single();if(a){let e={};"test"===s?(e.test_avg_quality=m(a.test_avg_quality,a.test_requests,r),i&&(e.test_avg_cost=m(a.test_avg_cost,a.test_requests,i))):(e.control_avg_quality=m(a.control_avg_quality,a.control_requests,r),i&&(e.control_avg_cost=m(a.control_avg_cost,a.control_requests,i))),await o.from("ab_test_assignments").update(e).eq("id",a.id)}}catch(e){}}function m(e,t,s){return e&&0!==t?(e*t+s)/(t+1):s}let g=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/quality-feedback/route",pathname:"/api/quality-feedback",filename:"route",bundlePath:"app/api/quality-feedback/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\quality-feedback\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:y}=g;function x(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410,5697],()=>s(61204));module.exports=r})();