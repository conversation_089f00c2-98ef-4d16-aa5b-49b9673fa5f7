exports.id=9805,exports.ids=[9805],exports.modules={39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},62706:(e,t,s)=>{"use strict";s.d(t,{cr:()=>a});var r=s(39398);async function a(e){try{let t=(0,r.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{data:s,error:a}=await t.from("subscriptions").select("tier, status").eq("user_id",e).eq("status","active").single();if(a||!s)return"free";return({starter:"starter",professional:"professional",enterprise:"enterprise"})[s.tier]||"free"}catch(e){return"free"}}},78335:()=>{},87846:(e,t,s)=>{"use strict";s.d(t,{R:()=>h});var r=s(88108),a=s(39398),i=s(55511),n=s.n(i);function o(){return(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}class c{constructor(){this.tierConfig={free:{enabled:!1,ttlHours:0,similarityThreshold:.9,maxCacheSize:0},starter:{enabled:!1,ttlHours:0,similarityThreshold:.9,maxCacheSize:0},professional:{enabled:!0,ttlHours:24,similarityThreshold:.85,maxCacheSize:1e3},enterprise:{enabled:!0,ttlHours:168,similarityThreshold:.8,maxCacheSize:1e4}}}static getInstance(){return c.instance||(c.instance=new c),c.instance}generatePromptHash(e,t,s){let r=`${e}|${t}|${s||0}`;return n().createHash("sha256").update(r).digest("hex")}isCacheEnabled(e){return this.tierConfig[e].enabled}getTierConfig(e){return this.tierConfig[e]}async searchCache(e,t,s,a){try{if(!this.isCacheEnabled(a))return null;let i=this.getTierConfig(a),n=o(),c=this.generatePromptHash(e.promptText,e.modelUsed,e.temperature),{data:h,error:u}=await n.from("semantic_cache").select("*").eq("prompt_hash",c).eq("user_id",t).eq("custom_api_config_id",s).gt("expires_at",new Date().toISOString()).limit(1).single();if(h&&!u)return await this.incrementHitCount(h.id),this.formatCachedResult(h,1);let d=await r.jinaEmbeddings.embedQuery(e.promptText),{data:l,error:p}=await n.rpc("search_semantic_cache",{query_embedding:d,config_id:s,user_id_param:t,similarity_threshold:i.similarityThreshold,match_count:1});if(p)return null;if(l&&l.length>0){let e=l[0];return await this.incrementHitCount(e.id),this.formatCachedResult(e,e.similarity)}return null}catch(e){return null}}async storeCache(e,t,s,a,i){try{if(!this.isCacheEnabled(i))return!1;let n=this.getTierConfig(i),c=o(),h=await r.jinaEmbeddings.embedQuery(e.promptText),u=this.generatePromptHash(e.promptText,e.modelUsed,e.temperature),d=new Date;d.setHours(d.getHours()+n.ttlHours);let{error:l}=await c.from("semantic_cache").insert({user_id:s,custom_api_config_id:a,prompt_text:e.promptText,prompt_embedding:h,prompt_hash:u,model_used:e.modelUsed,provider_used:e.providerUsed,temperature:e.temperature,max_tokens:e.maxTokens,request_metadata:e.metadata||{},response_data:t.responseData,response_tokens_prompt:t.tokensPrompt,response_tokens_completion:t.tokensCompletion,response_cost:t.cost,cache_tier:i,expires_at:d.toISOString()});if(l)return!1;return!0}catch(e){return!1}}async getCacheStats(e,t,s=7){try{let r=o(),a=new Date;a.setDate(a.getDate()-s);let{data:i,error:n}=await r.from("semantic_cache_analytics").select("*").eq("user_id",e).eq("custom_api_config_id",t).gte("date",a.toISOString().split("T")[0]);if(n||!i||0===i.length)return this.getEmptyStats();let c=i.reduce((e,t)=>({totalRequests:e.totalRequests+(t.total_requests||0),cacheHits:e.cacheHits+(t.cache_hits||0),cacheMisses:e.cacheMisses+(t.cache_misses||0),tokensSaved:e.tokensSaved+(t.tokens_saved||0),costSaved:e.costSaved+(t.cost_saved||0),responseTimeSum:e.responseTimeSum+(t.avg_response_time_ms||0),rowCount:e.rowCount+1}),{totalRequests:0,cacheHits:0,cacheMisses:0,tokensSaved:0,costSaved:0,responseTimeSum:0,rowCount:0});return{totalRequests:c.totalRequests,cacheHits:c.cacheHits,cacheMisses:c.cacheMisses,hitRate:c.totalRequests>0?c.cacheHits/c.totalRequests:0,tokensSaved:c.tokensSaved,costSaved:c.costSaved,avgResponseTime:c.rowCount>0?c.responseTimeSum/c.rowCount:0}}catch(e){return this.getEmptyStats()}}async cleanupExpiredCache(){try{let e=o(),{data:t,error:s}=await e.rpc("cleanup_semantic_cache");if(s)return 0;return t||0}catch(e){return 0}}async incrementHitCount(e){try{let t=o();await t.rpc("increment_cache_hit",{cache_id:e})}catch(e){}}formatCachedResult(e,t){return{id:e.id,promptText:e.prompt_text,responseData:e.response_data,modelUsed:e.model_used,providerUsed:e.provider_used,similarity:t,hitCount:e.hit_count,createdAt:e.created_at,expiresAt:e.expires_at}}getEmptyStats(){return{totalRequests:0,cacheHits:0,cacheMisses:0,hitRate:0,tokensSaved:0,costSaved:0,avgResponseTime:0}}}let h=c.getInstance()},88108:(e,t,s)=>{"use strict";s.d(t,{jinaEmbeddings:()=>a});class r{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,s=!1){let r=this.keyUsage.get(e);r&&(r.requests++,r.tokens+=t,r.lastUsed=new Date,s&&(r.errors++,r.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,s=null;for(let r=0;r<t;r++)try{let t=this.getBestKey(),s=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!s.ok){let e=await s.text();if(429===s.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${s.status}: ${e}`)}let r=await s.json();if(!r.data||0===r.data.length)throw Error("No embedding data returned from Jina API");let a=r.data[0].embedding;return this.updateKeyUsage(t,r.usage?.total_tokens||e.length),a}catch(e){if(s=e,r===t-1)break}throw Error(`All Jina API keys failed. Last error: ${s?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let s=0;s<e.length;s++){let r=await this.embedQuery(e[s]);t.push(r),s<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,s)=>{let r=this.keyUsage.get(t);r&&(e[`key_${s+1}`]={...r})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let a=new r},96487:()=>{}};