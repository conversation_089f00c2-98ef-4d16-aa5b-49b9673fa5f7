"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7871],{21394:(t,a,e)=>{e.d(a,{A:()=>s});var r=e(95155),n=e(11518),o=e.n(n);function s(t){let{className:a="",gridSize:e=40,opacity:n=.1,color:s="#000000",animated:c=!1,glowEffect:i=!1,variant:l="subtle"}=t;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{style:{...(()=>{let t=(t,a)=>"#000000"===t?"rgba(0, 0, 0, ".concat(a,")"):"#ffffff"===t?"rgba(255, 255, 255, ".concat(a,")"):"#ff6b35"===t?"rgba(255, 107, 53, ".concat(a,")"):"".concat(t).concat(Math.round(255*a).toString(16).padStart(2,"0")),a=3.2*n*.8;switch(l){case"tech":return{backgroundImage:"\n            linear-gradient(".concat(t(s,a)," 1px, transparent 1px),\n            linear-gradient(90deg, ").concat(t(s,a)," 1px, transparent 1px),\n            radial-gradient(circle at 50% 50%, ").concat(t(s,.5*a)," 2px, transparent 2px)\n          "),backgroundSize:"".concat(e,"px ").concat(e,"px, ").concat(e,"px ").concat(e,"px, ").concat(4*e,"px ").concat(4*e,"px"),animation:c?"tech-grid-move 30s linear infinite":"none",mask:"\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\n          ",maskComposite:"intersect",WebkitMask:"\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\n          ",WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:"\n            linear-gradient(".concat(t(s,a)," 0.5px, transparent 0.5px),\n            linear-gradient(90deg, ").concat(t(s,a)," 0.5px, transparent 0.5px),\n            linear-gradient(").concat(t(s,.7*a)," 1px, transparent 1px),\n            linear-gradient(90deg, ").concat(t(s,.7*a)," 1px, transparent 1px)\n          "),backgroundSize:"".concat(e,"px ").concat(e,"px, ").concat(e,"px ").concat(e,"px, ").concat(5*e,"px ").concat(5*e,"px, ").concat(5*e,"px ").concat(5*e,"px"),animation:c?"premium-grid-float 40s ease-in-out infinite":"none",mask:"\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\n          ",maskComposite:"intersect",WebkitMask:"\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\n          ",WebkitMaskComposite:"source-in"};default:return{backgroundImage:"\n            linear-gradient(".concat(t(s,a)," 1px, transparent 1px),\n            linear-gradient(90deg, ").concat(t(s,a)," 1px, transparent 1px)\n          "),backgroundSize:"".concat(e,"px ").concat(e,"px"),animation:c?"subtle-grid-drift 25s linear infinite":"none",mask:"\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\n          ",maskComposite:"intersect",WebkitMask:"\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\n          ",WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:i?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:o().dynamic([["cdf0235daf430a20",[e,e,.5*e,.3*e,e,.7*e,.3*e,e,.2*e,-.1*e,.1*e,.2*e,-.1*e,.1*e]]])+" "+"absolute inset-0 pointer-events-none ".concat(a)}),(0,r.jsx)(o(),{id:"cdf0235daf430a20",dynamic:[e,e,.5*e,.3*e,e,.7*e,.3*e,e,.2*e,-.1*e,.1*e,.2*e,-.1*e,.1*e],children:"@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(".concat(e,"px,").concat(e,"px);transform:translate(").concat(e,"px,").concat(e,"px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(").concat(e,"px,").concat(e,"px);transform:translate(").concat(e,"px,").concat(e,"px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(").concat(e,"px,").concat(e,"px);transform:translate(").concat(e,"px,").concat(e,"px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(").concat(e,"px,").concat(e,"px);-moz-transform:translate(").concat(e,"px,").concat(e,"px);-o-transform:translate(").concat(e,"px,").concat(e,"px);transform:translate(").concat(e,"px,").concat(e,"px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg)}50%{-webkit-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg)}75%{-webkit-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg)}50%{-moz-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg)}75%{-moz-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg)}50%{-o-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg)}75%{-o-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);-moz-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);-o-transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg);transform:translate(").concat(.5*e,"px,").concat(.3*e,"px)rotate(.5deg)}50%{-webkit-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);-moz-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);-o-transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg);transform:translate(").concat(e,"px,").concat(.7*e,"px)rotate(0deg)}75%{-webkit-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);-moz-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);-o-transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg);transform:translate(").concat(.3*e,"px,").concat(e,"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01)}50%{-webkit-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99)}75%{-webkit-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01)}50%{-moz-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99)}75%{-moz-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01)}50%{-o-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99)}75%{-o-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);-moz-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);-o-transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01);transform:translate(").concat(.2*e,"px,").concat(-.1*e,"px)scale(1.01)}50%{-webkit-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);-moz-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);-o-transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99);transform:translate(").concat(.1*e,"px,").concat(.2*e,"px)scale(.99)}75%{-webkit-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);-moz-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);-o-transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005);transform:translate(").concat(-.1*e,"px,").concat(.1*e,"px)scale(1.005)}}")})]})}},42490:(t,a,e)=>{e.d(a,{A:()=>i});var r=e(95155),n=e(55020),o=e(21394),s=e(42791);let c=[{icon:s.DQ,title:"Intelligent Role Routing",description:"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.",color:"text-blue-600",bgColor:"bg-blue-50"},{icon:s.tZ,title:"Unlimited API Requests",description:"No request limits, no usage caps, no overage fees. Pay only for your own API costs while enjoying unlimited access to 300+ AI models.",color:"text-green-600",bgColor:"bg-green-50"},{icon:s.Zu,title:"Enterprise Security",description:"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",color:"text-purple-600",bgColor:"bg-purple-50"},{icon:s.r9,title:"Comprehensive Analytics",description:"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.",color:"text-orange-600",bgColor:"bg-orange-50"},{icon:s.O4,title:"Performance Optimization",description:"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.",color:"text-indigo-600",bgColor:"bg-indigo-50"},{icon:s.xm,title:"Cost Optimization",description:"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",color:"text-emerald-600",bgColor:"bg-emerald-50"},{icon:s.YE,title:"300+ AI Models",description:"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",color:"text-red-600",bgColor:"bg-red-50"},{icon:s.Vy,title:"Advanced Routing Strategies",description:"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.",color:"text-cyan-600",bgColor:"bg-cyan-50"}];function i(){return(0,r.jsxs)("section",{id:"features",className:"relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-white py-20 relative",children:[(0,r.jsx)(o.A,{gridSize:45,opacity:.06,color:"#000000",variant:"premium",animated:!0,className:"absolute inset-0"}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)(n.PY1.h2,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3},className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight",children:["Enterprise-Grade",(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]",children:[" ","AI Infrastructure"]})]}),(0,r.jsx)(n.PY1.p,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.05},className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:c.slice(0,4).map((t,a)=>(0,r.jsxs)(n.PY1.div,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.05*a},className:"bg-white rounded-2xl p-8 shadow-lg border border-gray-200 hover:shadow-xl hover:border-[#ff6b35]/30 transition-all duration-300 group",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)(t.icon,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300",children:t.title}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:t.description})]},t.title))})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full overflow-hidden leading-none",children:(0,r.jsx)("svg",{className:"relative block w-full h-20","data-name":"Layer 1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1200 120",preserveAspectRatio:"none",children:(0,r.jsx)("path",{d:"M1200 120L0 16.48 0 0 1200 0 1200 120z",className:"fill-white"})})}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] via-[#f7931e] to-[#ff6b35] py-32 relative",children:[(0,r.jsx)(o.A,{gridSize:55,opacity:.12,color:"#ffffff",variant:"tech",animated:!0,glowEffect:!0,className:"absolute inset-0"}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:c.slice(4).map((t,a)=>(0,r.jsxs)(n.PY1.div,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.05*a},className:"bg-white rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl hover:border-white/40 transition-all duration-300 group backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg",children:(0,r.jsx)(t.icon,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300",children:t.title}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:t.description})]},t.title))})})]})]})]})}},78923:(t,a,e)=>{e.d(a,{A:()=>c});var r=e(95155),n=e(55020),o=e(45176),s=e(50956);function c(){return(0,r.jsxs)("section",{className:"py-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]"}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)(n.PY1.h2,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3},className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6",children:["Ready to Transform",(0,r.jsx)("br",{}),"Your AI Infrastructure?"]}),(0,r.jsx)(n.PY1.p,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.05},className:"text-xl text-white/90 mb-8 max-w-3xl mx-auto",children:"Join thousands of developers enjoying unlimited access to 300+ AI models. No request limits, no overage fees - just intelligent routing."}),(0,r.jsx)(n.PY1.div,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.1},className:"flex flex-wrap justify-center gap-6 mb-10",children:["Unlimited API requests","300+ AI models","Setup in 5 minutes","Cancel anytime"].map((t,a)=>(0,r.jsxs)("div",{className:"flex items-center text-white/90",children:[(0,r.jsx)(o.S,{className:"h-5 w-5 mr-2"}),(0,r.jsx)("span",{children:t})]},a))}),(0,r.jsxs)(n.PY1.div,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.15},className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[(0,r.jsxs)(s.A,{href:"/auth/signup?plan=professional",className:"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-semibold rounded-xl hover:bg-gray-50 transition-all duration-100 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:["Start Building Now",(0,r.jsx)(o.f,{className:"ml-2 h-5 w-5"})]}),(0,r.jsx)(s.A,{href:"/docs",className:"inline-flex items-center px-8 py-4 bg-transparent text-white font-semibold rounded-xl border-2 border-white/30 hover:border-white/50 hover:bg-white/10 transition-all duration-100",children:"View Documentation"})]}),(0,r.jsxs)(n.PY1.div,{initial:{opacity:0,y:15},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{duration:.3,delay:.2},className:"grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"∞"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"API Requests"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"300+"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"AI Models"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:"$0"}),(0,r.jsx)("div",{className:"text-white/80 text-sm",children:"Overage Fees"})]})]})]})})]})}}}]);