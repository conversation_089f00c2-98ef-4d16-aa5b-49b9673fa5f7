"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7525],{38736:(e,n,t)=>{t.d(n,{fP:()=>o,mf:()=>a,Br:()=>f,iZ:()=>m,LU:()=>d,Ld:()=>h,b8:()=>x,jW:()=>g,ph:()=>y,Qs:()=>v,mw:()=>H,Gg:()=>D,OS:()=>T,GK:()=>O,js:()=>V,om:()=>_,u4:()=>W,JL:()=>B,En:()=>j,p_:()=>U,AI:()=>Y,V2:()=>Q});var r=t(11603),i=t(49535),u=t(91877);let o={name:"attention",resolveAll:function(e,n){let t,i,o,l,a,s,f,m,d=-1;for(;++d<e.length;)if("enter"===e[d][0]&&"attentionSequence"===e[d][1].type&&e[d][1]._close){for(t=d;t--;)if("exit"===e[t][0]&&"attentionSequence"===e[t][1].type&&e[t][1]._open&&n.sliceSerialize(e[t][1]).charCodeAt(0)===n.sliceSerialize(e[d][1]).charCodeAt(0)){if((e[t][1]._close||e[d][1]._open)&&(e[d][1].end.offset-e[d][1].start.offset)%3&&!((e[t][1].end.offset-e[t][1].start.offset+e[d][1].end.offset-e[d][1].start.offset)%3))continue;s=e[t][1].end.offset-e[t][1].start.offset>1&&e[d][1].end.offset-e[d][1].start.offset>1?2:1;let p={...e[t][1].end},h={...e[d][1].start};c(p,-s),c(h,s),l={type:s>1?"strongSequence":"emphasisSequence",start:p,end:{...e[t][1].end}},a={type:s>1?"strongSequence":"emphasisSequence",start:{...e[d][1].start},end:h},o={type:s>1?"strongText":"emphasisText",start:{...e[t][1].end},end:{...e[d][1].start}},i={type:s>1?"strong":"emphasis",start:{...l.start},end:{...a.end}},e[t][1].end={...l.start},e[d][1].start={...a.end},f=[],e[t][1].end.offset-e[t][1].start.offset&&(f=(0,r.V)(f,[["enter",e[t][1],n],["exit",e[t][1],n]])),f=(0,r.V)(f,[["enter",i,n],["enter",l,n],["exit",l,n],["enter",o,n]]),f=(0,r.V)(f,(0,u.W)(n.parser.constructs.insideSpan.null,e.slice(t+1,d),n)),f=(0,r.V)(f,[["exit",o,n],["enter",a,n],["exit",a,n],["exit",i,n]]),e[d][1].end.offset-e[d][1].start.offset?(m=2,f=(0,r.V)(f,[["enter",e[d][1],n],["exit",e[d][1],n]])):m=0,(0,r.m)(e,t-1,d-t+3,f),d=t+f.length-m-2;break}}for(d=-1;++d<e.length;)"attentionSequence"===e[d][1].type&&(e[d][1].type="data");return e},tokenize:function(e,n){let t,r=this.parser.constructs.attentionMarkers.null,u=this.previous,o=(0,i.S)(u);return function(c){return t=c,e.enter("attentionSequence"),function c(l){if(l===t)return e.consume(l),c;let a=e.exit("attentionSequence"),s=(0,i.S)(l),f=!s||2===s&&o||r.includes(l),m=!o||2===o&&s||r.includes(u);return a._open=!!(42===t?f:f&&(o||!m)),a._close=!!(42===t?m:m&&(s||!f)),n(l)}(c)}}};function c(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}var l=t(12556);let a={name:"autolink",tokenize:function(e,n,t){let r=0;return function(n){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(n){return(0,l.CW)(n)?(e.consume(n),u):64===n?t(n):c(n)}function u(n){return 43===n||45===n||46===n||(0,l.lV)(n)?(r=1,function n(t){return 58===t?(e.consume(t),r=0,o):(43===t||45===t||46===t||(0,l.lV)(t))&&r++<32?(e.consume(t),n):(r=0,c(t))}(n)):c(n)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),n):null===r||32===r||60===r||(0,l.JQ)(r)?t(r):(e.consume(r),o)}function c(n){return 64===n?(e.consume(n),a):(0,l.cx)(n)?(e.consume(n),c):t(n)}function a(i){return(0,l.lV)(i)?function i(u){return 46===u?(e.consume(u),r=0,a):62===u?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(u),e.exit("autolinkMarker"),e.exit("autolink"),n):function n(u){if((45===u||(0,l.lV)(u))&&r++<63){let t=45===u?n:i;return e.consume(u),t}return t(u)}(u)}(i):t(i)}}};var s=t(94581);let f={partial:!0,tokenize:function(e,n,t){return function(n){return(0,l.On)(n)?(0,s.N)(e,r,"linePrefix")(n):r(n)};function r(e){return null===e||(0,l.HP)(e)?n(e):t(e)}}},m={continuation:{tokenize:function(e,n,t){let r=this;return function(n){return(0,l.On)(n)?(0,s.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):i(n)};function i(r){return e.attempt(m,n,t)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,n,t){let r=this;return function(n){if(62===n){let t=r.containerState;return t.open||(e.enter("blockQuote",{_container:!0}),t.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(n),e.exit("blockQuoteMarker"),i}return t(n)};function i(t){return(0,l.On)(t)?(e.enter("blockQuotePrefixWhitespace"),e.consume(t),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(t))}}},d={name:"characterEscape",tokenize:function(e,n,t){return function(n){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(n),e.exit("escapeMarker"),r};function r(r){return(0,l.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(r)}}};var p=t(43828);let h={name:"characterReference",tokenize:function(e,n,t){let r,i,u=this,o=0;return function(n){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(n),e.exit("characterReferenceMarker"),c};function c(n){return 35===n?(e.enter("characterReferenceMarkerNumeric"),e.consume(n),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),r=31,i=l.lV,s(n))}function a(n){return 88===n||120===n?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(n),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=l.ok,s):(e.enter("characterReferenceValue"),r=7,i=l.BM,s(n))}function s(c){if(59===c&&o){let r=e.exit("characterReferenceValue");return i!==l.lV||(0,p.s)(u.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(c),e.exit("characterReferenceMarker"),e.exit("characterReference"),n):t(c)}return i(c)&&o++<r?(e.consume(c),s):t(c)}}},k={partial:!0,tokenize:function(e,n,t){let r=this;return function(n){return null===n?t(n):(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?t(e):n(e)}}},x={concrete:!0,name:"codeFenced",tokenize:function(e,n,t){let r,i=this,u={partial:!0,tokenize:function(e,n,t){let u=0;return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),o};function o(n){return e.enter("codeFencedFence"),(0,l.On)(n)?(0,s.N)(e,a,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):a(n)}function a(n){return n===r?(e.enter("codeFencedFenceSequence"),function n(i){return i===r?(u++,e.consume(i),n):u>=c?(e.exit("codeFencedFenceSequence"),(0,l.On)(i)?(0,s.N)(e,f,"whitespace")(i):f(i)):t(i)}(n)):t(n)}function f(r){return null===r||(0,l.HP)(r)?(e.exit("codeFencedFence"),n(r)):t(r)}}},o=0,c=0;return function(n){var u=n;let f=i.events[i.events.length-1];return o=f&&"linePrefix"===f[1].type?f[2].sliceSerialize(f[1],!0).length:0,r=u,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function n(i){return i===r?(c++,e.consume(i),n):c<3?t(i):(e.exit("codeFencedFenceSequence"),(0,l.On)(i)?(0,s.N)(e,a,"whitespace")(i):a(i))}(u)};function a(u){return null===u||(0,l.HP)(u)?(e.exit("codeFencedFence"),i.interrupt?n(u):e.check(k,m,x)(u)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function n(i){return null===i||(0,l.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),a(i)):(0,l.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,s.N)(e,f,"whitespace")(i)):96===i&&i===r?t(i):(e.consume(i),n)}(u))}function f(n){return null===n||(0,l.HP)(n)?a(n):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function n(i){return null===i||(0,l.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),a(i)):96===i&&i===r?t(i):(e.consume(i),n)}(n))}function m(n){return e.attempt(u,x,d)(n)}function d(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),p}function p(n){return o>0&&(0,l.On)(n)?(0,s.N)(e,h,"linePrefix",o+1)(n):h(n)}function h(n){return null===n||(0,l.HP)(n)?e.check(k,m,x)(n):(e.enter("codeFlowValue"),function n(t){return null===t||(0,l.HP)(t)?(e.exit("codeFlowValue"),h(t)):(e.consume(t),n)}(n))}function x(t){return e.exit("codeFenced"),n(t)}}},g={name:"codeIndented",tokenize:function(e,n,t){let r=this;return function(n){return e.enter("codeIndented"),(0,s.N)(e,i,"linePrefix",5)(n)};function i(n){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function n(t){return null===t?u(t):(0,l.HP)(t)?e.attempt(b,n,u)(t):(e.enter("codeFlowValue"),function t(r){return null===r||(0,l.HP)(r)?(e.exit("codeFlowValue"),n(r)):(e.consume(r),t)}(t))}(n):t(n)}function u(t){return e.exit("codeIndented"),n(t)}}},b={partial:!0,tokenize:function(e,n,t){let r=this;return i;function i(n){return r.parser.lazy[r.now().line]?t(n):(0,l.HP)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i):(0,s.N)(e,u,"linePrefix",5)(n)}function u(e){let u=r.events[r.events.length-1];return u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4?n(e):(0,l.HP)(e)?i(e):t(e)}}},y={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let n,t,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(n=i;++n<r;)if("codeTextData"===e[n][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(n=i-1,r++;++n<=r;)void 0===t?n!==r&&"lineEnding"!==e[n][1].type&&(t=n):(n===r||"lineEnding"===e[n][1].type)&&(e[t][1].type="codeTextData",n!==t+2&&(e[t][1].end=e[n-1][1].end,e.splice(t+2,n-t-2),r-=n-t-2,n=t+2),t=void 0);return e},tokenize:function(e,n,t){let r,i,u=0;return function(n){return e.enter("codeText"),e.enter("codeTextSequence"),function n(t){return 96===t?(e.consume(t),u++,n):(e.exit("codeTextSequence"),o(t))}(n)};function o(a){return null===a?t(a):32===a?(e.enter("space"),e.consume(a),e.exit("space"),o):96===a?(i=e.enter("codeTextSequence"),r=0,function t(o){return 96===o?(e.consume(o),r++,t):r===u?(e.exit("codeTextSequence"),e.exit("codeText"),n(o)):(i.type="codeTextData",c(o))}(a)):(0,l.HP)(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),o):(e.enter("codeTextData"),c(a))}function c(n){return null===n||32===n||96===n||(0,l.HP)(n)?(e.exit("codeTextData"),o(n)):(e.consume(n),c)}}};var S=t(58043);let v={resolve:function(e){return(0,S.w)(e),e},tokenize:function(e,n){let t;return function(n){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),r(n)};function r(n){return null===n?i(n):(0,l.HP)(n)?e.check(F,u,i)(n):(e.consume(n),r)}function i(t){return e.exit("chunkContent"),e.exit("content"),n(t)}function u(n){return e.consume(n),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,r}}},F={partial:!0,tokenize:function(e,n,t){let r=this;return function(n){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,s.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,l.HP)(i))return t(i);let u=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&u&&"linePrefix"===u[1].type&&u[2].sliceSerialize(u[1],!0).length>=4?n(i):e.interrupt(r.parser.constructs.flow,t,n)(i)}}};function P(e,n,t,r,i,u,o,c,a){let s=a||Number.POSITIVE_INFINITY,f=0;return function(n){return 60===n?(e.enter(r),e.enter(i),e.enter(u),e.consume(n),e.exit(u),m):null===n||32===n||41===n||(0,l.JQ)(n)?t(n):(e.enter(r),e.enter(o),e.enter(c),e.enter("chunkString",{contentType:"string"}),h(n))};function m(t){return 62===t?(e.enter(u),e.consume(t),e.exit(u),e.exit(i),e.exit(r),n):(e.enter(c),e.enter("chunkString",{contentType:"string"}),d(t))}function d(n){return 62===n?(e.exit("chunkString"),e.exit(c),m(n)):null===n||60===n||(0,l.HP)(n)?t(n):(e.consume(n),92===n?p:d)}function p(n){return 60===n||62===n||92===n?(e.consume(n),d):d(n)}function h(i){return!f&&(null===i||41===i||(0,l.Ee)(i))?(e.exit("chunkString"),e.exit(c),e.exit(o),e.exit(r),n(i)):f<s&&40===i?(e.consume(i),f++,h):41===i?(e.consume(i),f--,h):null===i||32===i||40===i||(0,l.JQ)(i)?t(i):(e.consume(i),92===i?k:h)}function k(n){return 40===n||41===n||92===n?(e.consume(n),h):h(n)}}function E(e,n,t,r,i,u){let o,c=this,a=0;return function(n){return e.enter(r),e.enter(i),e.consume(n),e.exit(i),e.enter(u),s};function s(m){return a>999||null===m||91===m||93===m&&!o||94===m&&!a&&"_hiddenFootnoteSupport"in c.parser.constructs?t(m):93===m?(e.exit(u),e.enter(i),e.consume(m),e.exit(i),e.exit(r),n):(0,l.HP)(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),f(m))}function f(n){return null===n||91===n||93===n||(0,l.HP)(n)||a++>999?(e.exit("chunkString"),s(n)):(e.consume(n),o||(o=!(0,l.On)(n)),92===n?m:f)}function m(n){return 91===n||92===n||93===n?(e.consume(n),a++,f):f(n)}}function C(e,n,t,r,i,u){let o;return function(n){return 34===n||39===n||40===n?(e.enter(r),e.enter(i),e.consume(n),e.exit(i),o=40===n?41:n,c):t(n)};function c(t){return t===o?(e.enter(i),e.consume(t),e.exit(i),e.exit(r),n):(e.enter(u),a(t))}function a(n){return n===o?(e.exit(u),c(o)):null===n?t(n):(0,l.HP)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,s.N)(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),f(n))}function f(n){return n===o||null===n||(0,l.HP)(n)?(e.exit("chunkString"),a(n)):(e.consume(n),92===n?m:f)}function m(n){return n===o||92===n?(e.consume(n),f):f(n)}}function z(e,n){let t;return function r(i){return(0,l.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):(0,l.On)(i)?(0,s.N)(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}var M=t(33386);let H={name:"definition",tokenize:function(e,n,t){let r,i=this;return function(n){var r;return e.enter("definition"),r=n,E.call(i,e,u,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function u(n){return(r=(0,M.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===n)?(e.enter("definitionMarker"),e.consume(n),e.exit("definitionMarker"),o):t(n)}function o(n){return(0,l.Ee)(n)?z(e,c)(n):c(n)}function c(n){return P(e,a,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(n)}function a(n){return e.attempt(w,f,f)(n)}function f(n){return(0,l.On)(n)?(0,s.N)(e,m,"whitespace")(n):m(n)}function m(u){return null===u||(0,l.HP)(u)?(e.exit("definition"),i.parser.defined.push(r),n(u)):t(u)}}},w={partial:!0,tokenize:function(e,n,t){return function(n){return(0,l.Ee)(n)?z(e,r)(n):t(n)};function r(n){return C(e,i,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(n)}function i(n){return(0,l.On)(n)?(0,s.N)(e,u,"whitespace")(n):u(n)}function u(e){return null===e||(0,l.HP)(e)?n(e):t(e)}}},D={name:"hardBreakEscape",tokenize:function(e,n,t){return function(n){return e.enter("hardBreakEscape"),e.consume(n),r};function r(r){return(0,l.HP)(r)?(e.exit("hardBreakEscape"),n(r)):t(r)}}},T={name:"headingAtx",resolve:function(e,n){let t,i,u=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),u-2>o&&"whitespace"===e[u][1].type&&(u-=2),"atxHeadingSequence"===e[u][1].type&&(o===u-1||u-4>o&&"whitespace"===e[u-2][1].type)&&(u-=o+1===u?2:4),u>o&&(t={type:"atxHeadingText",start:e[o][1].start,end:e[u][1].end},i={type:"chunkText",start:e[o][1].start,end:e[u][1].end,contentType:"text"},(0,r.m)(e,o,u-o+1,[["enter",t,n],["enter",i,n],["exit",i,n],["exit",t,n]])),e},tokenize:function(e,n,t){let r=0;return function(i){var u;return e.enter("atxHeading"),u=i,e.enter("atxHeadingSequence"),function i(u){return 35===u&&r++<6?(e.consume(u),i):null===u||(0,l.Ee)(u)?(e.exit("atxHeadingSequence"),function t(r){return 35===r?(e.enter("atxHeadingSequence"),function n(r){return 35===r?(e.consume(r),n):(e.exit("atxHeadingSequence"),t(r))}(r)):null===r||(0,l.HP)(r)?(e.exit("atxHeading"),n(r)):(0,l.On)(r)?(0,s.N)(e,t,"whitespace")(r):(e.enter("atxHeadingText"),function n(r){return null===r||35===r||(0,l.Ee)(r)?(e.exit("atxHeadingText"),t(r)):(e.consume(r),n)}(r))}(u)):t(u)}(u)}}};var L=t(97599);let O={concrete:!0,name:"htmlFlow",resolveTo:function(e){let n=e.length;for(;n--&&("enter"!==e[n][0]||"htmlFlow"!==e[n][1].type););return n>1&&"linePrefix"===e[n-2][1].type&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e},tokenize:function(e,n,t){let r,i,u,o,c,a=this;return function(n){var t;return t=n,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),s};function s(o){return 33===o?(e.consume(o),f):47===o?(e.consume(o),i=!0,p):63===o?(e.consume(o),r=3,a.interrupt?n:O):(0,l.CW)(o)?(e.consume(o),u=String.fromCharCode(o),h):t(o)}function f(i){return 45===i?(e.consume(i),r=2,m):91===i?(e.consume(i),r=5,o=0,d):(0,l.CW)(i)?(e.consume(i),r=4,a.interrupt?n:O):t(i)}function m(r){return 45===r?(e.consume(r),a.interrupt?n:O):t(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?a.interrupt?n:E:d:t(r)}function p(n){return(0,l.CW)(n)?(e.consume(n),u=String.fromCharCode(n),h):t(n)}function h(o){if(null===o||47===o||62===o||(0,l.Ee)(o)){let c=47===o,s=u.toLowerCase();return!c&&!i&&L.y.includes(s)?(r=1,a.interrupt?n(o):E(o)):L.T.includes(u.toLowerCase())?(r=6,c)?(e.consume(o),k):a.interrupt?n(o):E(o):(r=7,a.interrupt&&!a.parser.lazy[a.now().line]?t(o):i?function n(t){return(0,l.On)(t)?(e.consume(t),n):F(t)}(o):x(o))}return 45===o||(0,l.lV)(o)?(e.consume(o),u+=String.fromCharCode(o),h):t(o)}function k(r){return 62===r?(e.consume(r),a.interrupt?n:E):t(r)}function x(n){return 47===n?(e.consume(n),F):58===n||95===n||(0,l.CW)(n)?(e.consume(n),g):(0,l.On)(n)?(e.consume(n),x):F(n)}function g(n){return 45===n||46===n||58===n||95===n||(0,l.lV)(n)?(e.consume(n),g):b(n)}function b(n){return 61===n?(e.consume(n),y):(0,l.On)(n)?(e.consume(n),b):x(n)}function y(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),c=n,S):(0,l.On)(n)?(e.consume(n),y):function n(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||(0,l.Ee)(t)?b(t):(e.consume(t),n)}(n)}function S(n){return n===c?(e.consume(n),c=null,v):null===n||(0,l.HP)(n)?t(n):(e.consume(n),S)}function v(e){return 47===e||62===e||(0,l.On)(e)?x(e):t(e)}function F(n){return 62===n?(e.consume(n),P):t(n)}function P(n){return null===n||(0,l.HP)(n)?E(n):(0,l.On)(n)?(e.consume(n),P):t(n)}function E(n){return 45===n&&2===r?(e.consume(n),H):60===n&&1===r?(e.consume(n),w):62===n&&4===r?(e.consume(n),V):63===n&&3===r?(e.consume(n),O):93===n&&5===r?(e.consume(n),T):(0,l.HP)(n)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(I,_,C)(n)):null===n||(0,l.HP)(n)?(e.exit("htmlFlowData"),C(n)):(e.consume(n),E)}function C(n){return e.check(N,z,_)(n)}function z(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),M}function M(n){return null===n||(0,l.HP)(n)?C(n):(e.enter("htmlFlowData"),E(n))}function H(n){return 45===n?(e.consume(n),O):E(n)}function w(n){return 47===n?(e.consume(n),u="",D):E(n)}function D(n){if(62===n){let t=u.toLowerCase();return L.y.includes(t)?(e.consume(n),V):E(n)}return(0,l.CW)(n)&&u.length<8?(e.consume(n),u+=String.fromCharCode(n),D):E(n)}function T(n){return 93===n?(e.consume(n),O):E(n)}function O(n){return 62===n?(e.consume(n),V):45===n&&2===r?(e.consume(n),O):E(n)}function V(n){return null===n||(0,l.HP)(n)?(e.exit("htmlFlowData"),_(n)):(e.consume(n),V)}function _(t){return e.exit("htmlFlow"),n(t)}}},I={partial:!0,tokenize:function(e,n,t){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(f,n,t)}}},N={partial:!0,tokenize:function(e,n,t){let r=this;return function(n){return(0,l.HP)(n)?(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),i):t(n)};function i(e){return r.parser.lazy[r.now().line]?t(e):n(e)}}},V={name:"htmlText",tokenize:function(e,n,t){let r,i,u,o=this;return function(n){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(n),c};function c(n){return 33===n?(e.consume(n),a):47===n?(e.consume(n),v):63===n?(e.consume(n),y):(0,l.CW)(n)?(e.consume(n),P):t(n)}function a(n){return 45===n?(e.consume(n),f):91===n?(e.consume(n),i=0,h):(0,l.CW)(n)?(e.consume(n),b):t(n)}function f(n){return 45===n?(e.consume(n),p):t(n)}function m(n){return null===n?t(n):45===n?(e.consume(n),d):(0,l.HP)(n)?(u=m,T(n)):(e.consume(n),m)}function d(n){return 45===n?(e.consume(n),p):m(n)}function p(e){return 62===e?D(e):45===e?d(e):m(e)}function h(n){let r="CDATA[";return n===r.charCodeAt(i++)?(e.consume(n),i===r.length?k:h):t(n)}function k(n){return null===n?t(n):93===n?(e.consume(n),x):(0,l.HP)(n)?(u=k,T(n)):(e.consume(n),k)}function x(n){return 93===n?(e.consume(n),g):k(n)}function g(n){return 62===n?D(n):93===n?(e.consume(n),g):k(n)}function b(n){return null===n||62===n?D(n):(0,l.HP)(n)?(u=b,T(n)):(e.consume(n),b)}function y(n){return null===n?t(n):63===n?(e.consume(n),S):(0,l.HP)(n)?(u=y,T(n)):(e.consume(n),y)}function S(e){return 62===e?D(e):y(e)}function v(n){return(0,l.CW)(n)?(e.consume(n),F):t(n)}function F(n){return 45===n||(0,l.lV)(n)?(e.consume(n),F):function n(t){return(0,l.HP)(t)?(u=n,T(t)):(0,l.On)(t)?(e.consume(t),n):D(t)}(n)}function P(n){return 45===n||(0,l.lV)(n)?(e.consume(n),P):47===n||62===n||(0,l.Ee)(n)?E(n):t(n)}function E(n){return 47===n?(e.consume(n),D):58===n||95===n||(0,l.CW)(n)?(e.consume(n),C):(0,l.HP)(n)?(u=E,T(n)):(0,l.On)(n)?(e.consume(n),E):D(n)}function C(n){return 45===n||46===n||58===n||95===n||(0,l.lV)(n)?(e.consume(n),C):function n(t){return 61===t?(e.consume(t),z):(0,l.HP)(t)?(u=n,T(t)):(0,l.On)(t)?(e.consume(t),n):E(t)}(n)}function z(n){return null===n||60===n||61===n||62===n||96===n?t(n):34===n||39===n?(e.consume(n),r=n,M):(0,l.HP)(n)?(u=z,T(n)):(0,l.On)(n)?(e.consume(n),z):(e.consume(n),H)}function M(n){return n===r?(e.consume(n),r=void 0,w):null===n?t(n):(0,l.HP)(n)?(u=M,T(n)):(e.consume(n),M)}function H(n){return null===n||34===n||39===n||60===n||61===n||96===n?t(n):47===n||62===n||(0,l.Ee)(n)?E(n):(e.consume(n),H)}function w(e){return 47===e||62===e||(0,l.Ee)(e)?E(e):t(e)}function D(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),n):t(r)}function T(n){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),L}function L(n){return(0,l.On)(n)?(0,s.N)(e,O,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):O(n)}function O(n){return e.enter("htmlTextData"),u(n)}}},_={name:"labelEnd",resolveAll:function(e){let n=-1,t=[];for(;++n<e.length;){let r=e[n][1];if(t.push(e[n]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",n+=e}}return e.length!==t.length&&(0,r.m)(e,0,e.length,t),e},resolveTo:function(e,n){let t,i,o,c,l=e.length,a=0;for(;l--;)if(t=e[l][1],i){if("link"===t.type||"labelLink"===t.type&&t._inactive)break;"enter"===e[l][0]&&"labelLink"===t.type&&(t._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===t.type||"labelLink"===t.type)&&!t._balanced&&(i=l,"labelLink"!==t.type)){a=2;break}}else"labelEnd"===t.type&&(o=l);let s={type:"labelLink"===e[i][1].type?"link":"image",start:{...e[i][1].start},end:{...e[e.length-1][1].end}},f={type:"label",start:{...e[i][1].start},end:{...e[o][1].end}},m={type:"labelText",start:{...e[i+a+2][1].end},end:{...e[o-2][1].start}};return c=[["enter",s,n],["enter",f,n]],c=(0,r.V)(c,e.slice(i+1,i+a+3)),c=(0,r.V)(c,[["enter",m,n]]),c=(0,r.V)(c,(0,u.W)(n.parser.constructs.insideSpan.null,e.slice(i+a+4,o-3),n)),c=(0,r.V)(c,[["exit",m,n],e[o-2],e[o-1],["exit",f,n]]),c=(0,r.V)(c,e.slice(o+1)),c=(0,r.V)(c,[["exit",s,n]]),(0,r.m)(e,i,e.length,c),e},tokenize:function(e,n,t){let r,i,u=this,o=u.events.length;for(;o--;)if(("labelImage"===u.events[o][1].type||"labelLink"===u.events[o][1].type)&&!u.events[o][1]._balanced){r=u.events[o][1];break}return function(n){return r?r._inactive?s(n):(i=u.parser.defined.includes((0,M.B)(u.sliceSerialize({start:r.end,end:u.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelEnd"),c):t(n)};function c(n){return 40===n?e.attempt(A,a,i?a:s)(n):91===n?e.attempt(q,a,i?l:s)(n):i?a(n):s(n)}function l(n){return e.attempt(R,a,s)(n)}function a(e){return n(e)}function s(e){return r._balanced=!0,t(e)}}},A={tokenize:function(e,n,t){return function(n){return e.enter("resource"),e.enter("resourceMarker"),e.consume(n),e.exit("resourceMarker"),r};function r(n){return(0,l.Ee)(n)?z(e,i)(n):i(n)}function i(n){return 41===n?s(n):P(e,u,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(n)}function u(n){return(0,l.Ee)(n)?z(e,c)(n):s(n)}function o(e){return t(e)}function c(n){return 34===n||39===n||40===n?C(e,a,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(n):s(n)}function a(n){return(0,l.Ee)(n)?z(e,s)(n):s(n)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),n):t(r)}}},q={tokenize:function(e,n,t){let r=this;return function(n){return E.call(r,e,i,u,"reference","referenceMarker","referenceString")(n)};function i(e){return r.parser.defined.includes((0,M.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(e):t(e)}function u(e){return t(e)}}},R={tokenize:function(e,n,t){return function(n){return e.enter("reference"),e.enter("referenceMarker"),e.consume(n),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),n):t(r)}}},W={name:"labelStartImage",resolveAll:_.resolveAll,tokenize:function(e,n,t){let r=this;return function(n){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(n),e.exit("labelImageMarker"),i};function i(n){return 91===n?(e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelImage"),u):t(n)}function u(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}}},B={name:"labelStartLink",resolveAll:_.resolveAll,tokenize:function(e,n,t){let r=this;return function(n){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(n),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?t(e):n(e)}}},j={name:"lineEnding",tokenize:function(e,n){return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,s.N)(e,n,"linePrefix")}}},Q={name:"thematicBreak",tokenize:function(e,n,t){let r,i=0;return function(u){var o;return e.enter("thematicBreak"),r=o=u,function u(o){return o===r?(e.enter("thematicBreakSequence"),function n(t){return t===r?(e.consume(t),i++,n):(e.exit("thematicBreakSequence"),(0,l.On)(t)?(0,s.N)(e,u,"whitespace")(t):u(t))}(o)):i>=3&&(null===o||(0,l.HP)(o))?(e.exit("thematicBreak"),n(o)):t(o)}(o)}}},U={continuation:{tokenize:function(e,n,t){let r=this;return r.containerState._closeFlow=void 0,e.check(f,function(t){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,s.N)(e,n,"listItemIndent",r.containerState.size+1)(t)},function(t){return r.containerState.furtherBlankLines||!(0,l.On)(t)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(t)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(G,n,i)(t))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,s.N)(e,e.attempt(U,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,n,t){let r=this,i=r.events[r.events.length-1],u=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(n){let i=r.containerState.type||(42===n||43===n||45===n?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||n===r.containerState.marker:(0,l.BM)(n)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===n||45===n?e.check(Q,t,c)(n):c(n);if(!r.interrupt||49===n)return e.enter("listItemPrefix"),e.enter("listItemValue"),function n(i){return(0,l.BM)(i)&&++o<10?(e.consume(i),n):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),c(i)):t(i)}(n)}return t(n)};function c(n){return e.enter("listItemMarker"),e.consume(n),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||n,e.check(f,r.interrupt?t:a,e.attempt(J,m,s))}function a(e){return r.containerState.initialBlankLine=!0,u++,m(e)}function s(n){return(0,l.On)(n)?(e.enter("listItemPrefixWhitespace"),e.consume(n),e.exit("listItemPrefixWhitespace"),m):t(n)}function m(t){return r.containerState.size=u+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(t)}}},J={partial:!0,tokenize:function(e,n,t){let r=this;return(0,s.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,l.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?n(e):t(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},G={partial:!0,tokenize:function(e,n,t){let r=this;return(0,s.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?n(e):t(e)},"listItemIndent",r.containerState.size+1)}},Y={name:"setextUnderline",resolveTo:function(e,n){let t,r,i,u=e.length;for(;u--;)if("enter"===e[u][0]){if("content"===e[u][1].type){t=u;break}"paragraph"===e[u][1].type&&(r=u)}else"content"===e[u][1].type&&e.splice(u,1),i||"definition"!==e[u][1].type||(i=u);let o={type:"setextHeading",start:{...e[t][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,n]),e.splice(i+1,0,["exit",e[t][1],n]),e[t][1].end={...e[i][1].end}):e[t][1]=o,e.push(["exit",o,n]),e},tokenize:function(e,n,t){let r,i=this;return function(n){var o;let c,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){c="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||c)?(e.enter("setextHeadingLine"),r=n,o=n,e.enter("setextHeadingLineSequence"),function n(t){return t===r?(e.consume(t),n):(e.exit("setextHeadingLineSequence"),(0,l.On)(t)?(0,s.N)(e,u,"lineSuffix")(t):u(t))}(o)):t(n)};function u(r){return null===r||(0,l.HP)(r)?(e.exit("setextHeadingLine"),n(r)):t(r)}}}},47525:(e,n,t)=>{t.d(n,{T:()=>W});var r=t(69381),i=t(12556);let u={tokenize:function(e,n,t){let r=0;return function n(u){return(87===u||119===u)&&r<3?(r++,e.consume(u),n):46===u&&3===r?(e.consume(u),i):t(u)};function i(e){return null===e?t(e):n(e)}},partial:!0},o={tokenize:function(e,n,t){let r,u,o;return c;function c(n){return 46===n||95===n?e.check(l,s,a)(n):null===n||(0,i.Ee)(n)||(0,i.Ny)(n)||45!==n&&(0,i.es)(n)?s(n):(o=!0,e.consume(n),c)}function a(n){return 95===n?r=!0:(u=r,r=void 0),e.consume(n),c}function s(e){return u||r||!o?t(e):n(e)}},partial:!0},c={tokenize:function(e,n){let t=0,r=0;return u;function u(c){return 40===c?(t++,e.consume(c),u):41===c&&r<t?o(c):33===c||34===c||38===c||39===c||41===c||42===c||44===c||46===c||58===c||59===c||60===c||63===c||93===c||95===c||126===c?e.check(l,n,o)(c):null===c||(0,i.Ee)(c)||(0,i.Ny)(c)?n(c):(e.consume(c),u)}function o(n){return 41===n&&r++,e.consume(n),u}},partial:!0},l={tokenize:function(e,n,t){return r;function r(c){return 33===c||34===c||39===c||41===c||42===c||44===c||46===c||58===c||59===c||63===c||95===c||126===c?(e.consume(c),r):38===c?(e.consume(c),o):93===c?(e.consume(c),u):60===c||null===c||(0,i.Ee)(c)||(0,i.Ny)(c)?n(c):t(c)}function u(e){return null===e||40===e||91===e||(0,i.Ee)(e)||(0,i.Ny)(e)?n(e):r(e)}function o(n){return(0,i.CW)(n)?function n(u){return 59===u?(e.consume(u),r):(0,i.CW)(u)?(e.consume(u),n):t(u)}(n):t(n)}},partial:!0},a={tokenize:function(e,n,t){return function(n){return e.consume(n),r};function r(e){return(0,i.lV)(e)?t(e):n(e)}},partial:!0},s={name:"wwwAutolink",tokenize:function(e,n,t){let r=this;return function(n){return 87!==n&&119!==n||!h.call(r,r.previous)||b(r.events)?t(n):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(u,e.attempt(o,e.attempt(c,i),t),t)(n))};function i(t){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),n(t)}},previous:h},f={name:"protocolAutolink",tokenize:function(e,n,t){let r=this,u="",l=!1;return function(n){return(72===n||104===n)&&k.call(r,r.previous)&&!b(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),u+=String.fromCodePoint(n),e.consume(n),a):t(n)};function a(n){if((0,i.CW)(n)&&u.length<5)return u+=String.fromCodePoint(n),e.consume(n),a;if(58===n){let t=u.toLowerCase();if("http"===t||"https"===t)return e.consume(n),s}return t(n)}function s(n){return 47===n?(e.consume(n),l)?f:(l=!0,s):t(n)}function f(n){return null===n||(0,i.JQ)(n)||(0,i.Ee)(n)||(0,i.Ny)(n)||(0,i.es)(n)?t(n):e.attempt(o,e.attempt(c,m),t)(n)}function m(t){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),n(t)}},previous:k},m={name:"emailAutolink",tokenize:function(e,n,t){let r,u,o=this;return function(n){return!g(n)||!x.call(o,o.previous)||b(o.events)?t(n):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function n(r){return g(r)?(e.consume(r),n):64===r?(e.consume(r),c):t(r)}(n))};function c(n){return 46===n?e.check(a,s,l)(n):45===n||95===n||(0,i.lV)(n)?(u=!0,e.consume(n),c):s(n)}function l(n){return e.consume(n),r=!0,c}function s(c){return u&&r&&(0,i.CW)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),n(c)):t(c)}},previous:x},d={},p=48;for(;p<123;)d[p]=m,58==++p?p=65:91===p&&(p=97);function h(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,i.Ee)(e)}function k(e){return!(0,i.CW)(e)}function x(e){return!(47===e||g(e))}function g(e){return 43===e||45===e||46===e||95===e||(0,i.lV)(e)}function b(e){let n=e.length,t=!1;for(;n--;){let r=e[n][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){t=!0;break}if(r._gfmAutolinkLiteralWalkedInto){t=!1;break}}return e.length>0&&!t&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),t}d[43]=m,d[45]=m,d[46]=m,d[95]=m,d[72]=[m,f],d[104]=[m,f],d[87]=[m,s],d[119]=[m,s],t(25583);function y(e,n){let t=this.sliceSerialize(e);this.tag('<a href="'+sanitizeUri((n||"")+t)+'">'),this.raw(this.encode(t)),this.tag("</a>")}var S=t(38736),v=t(94581),F=t(33386);let P={tokenize:function(e,n,t){let r=this;return(0,v.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?n(e):t(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function E(e,n,t){let r,i=this,u=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;u--;){let e=i.events[u][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(u){if(!r||!r._balanced)return t(u);let c=(0,F.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===c.codePointAt(0)&&o.includes(c.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),n(u)):t(u)}}function C(e,n){let t=e.length;for(;t--;)if("labelImage"===e[t][1].type&&"enter"===e[t][0]){e[t][1];break}e[t+1][1].type="data",e[t+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[t+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[t+3][1].end),end:Object.assign({},e[t+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let u={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},u.start),end:Object.assign({},u.end)},c=[e[t+1],e[t+2],["enter",r,n],e[t+3],e[t+4],["enter",i,n],["exit",i,n],["enter",u,n],["enter",o,n],["exit",o,n],["exit",u,n],e[e.length-2],e[e.length-1],["exit",r,n]];return e.splice(t,e.length-t+1,...c),e}function z(e,n,t){let r,u=this,o=u.parser.gfmFootnotes||(u.parser.gfmFootnotes=[]),c=0;return function(n){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(n),e.exit("gfmFootnoteCallLabelMarker"),l};function l(n){return 94!==n?t(n):(e.enter("gfmFootnoteCallMarker"),e.consume(n),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",a)}function a(l){if(c>999||93===l&&!r||null===l||91===l||(0,i.Ee)(l))return t(l);if(93===l){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,F.B)(u.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),n):t(l)}return(0,i.Ee)(l)||(r=!0),c++,e.consume(l),92===l?s:a}function s(n){return 91===n||92===n||93===n?(e.consume(n),c++,a):a(n)}}function M(e,n,t){let r,u,o=this,c=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),l=0;return function(n){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(n),e.exit("gfmFootnoteDefinitionLabelMarker"),a};function a(n){return 94===n?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(n),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",s):t(n)}function s(n){if(l>999||93===n&&!u||null===n||91===n||(0,i.Ee)(n))return t(n);if(93===n){e.exit("chunkString");let t=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,F.B)(o.sliceSerialize(t)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(n),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),m}return(0,i.Ee)(n)||(u=!0),l++,e.consume(n),92===n?f:s}function f(n){return 91===n||92===n||93===n?(e.consume(n),l++,s):s(n)}function m(n){return 58===n?(e.enter("definitionMarker"),e.consume(n),e.exit("definitionMarker"),c.includes(r)||c.push(r),(0,v.N)(e,d,"gfmFootnoteDefinitionWhitespace")):t(n)}function d(e){return n(e)}}function H(e,n,t){return e.check(S.Br,n,e.attempt(P,n,t))}function w(e){e.exit("gfmFootnoteDefinition")}var D=t(11603),T=t(49535),L=t(91877);class O{constructor(){this.map=[]}add(e,n,t){!function(e,n,t,r){let i=0;if(0!==t||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===n){e.map[i][1]+=t,e.map[i][2].push(...r);return}i+=1}e.map.push([n,t,r])}}(this,e,n,t)}consume(e){if(this.map.sort(function(e,n){return e[0]-n[0]}),0===this.map.length)return;let n=this.map.length,t=[];for(;n>0;)n-=1,t.push(e.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),e.length=this.map[n][0];t.push(e.slice()),e.length=0;let r=t.pop();for(;r;){for(let n of r)e.push(n);r=t.pop()}this.map.length=0}}function I(e,n,t){let r,u=this,o=0,c=0;return function(e){let n=u.events.length-1;for(;n>-1;){let e=u.events[n][1].type;if("lineEnding"===e||"linePrefix"===e)n--;else break}let r=n>-1?u.events[n][1].type:null,i="tableHead"===r||"tableRow"===r?b:l;return i===b&&u.parser.lazy[u.now().line]?t(e):i(e)};function l(n){var t;return e.enter("tableHead"),e.enter("tableRow"),124===(t=n)||(r=!0,c+=1),a(t)}function a(n){return null===n?t(n):(0,i.HP)(n)?c>1?(c=0,u.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),m):t(n):(0,i.On)(n)?(0,v.N)(e,a,"whitespace")(n):(c+=1,r&&(r=!1,o+=1),124===n)?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),r=!0,a):(e.enter("data"),s(n))}function s(n){return null===n||124===n||(0,i.Ee)(n)?(e.exit("data"),a(n)):(e.consume(n),92===n?f:s)}function f(n){return 92===n||124===n?(e.consume(n),s):s(n)}function m(n){return(u.interrupt=!1,u.parser.lazy[u.now().line])?t(n):(e.enter("tableDelimiterRow"),r=!1,(0,i.On)(n))?(0,v.N)(e,d,"linePrefix",u.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(n):d(n)}function d(n){return 45===n||58===n?h(n):124===n?(r=!0,e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),p):t(n)}function p(n){return(0,i.On)(n)?(0,v.N)(e,h,"whitespace")(n):h(n)}function h(n){return 58===n?(c+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),k):45===n?(c+=1,k(n)):null===n||(0,i.HP)(n)?g(n):t(n)}function k(n){return 45===n?(e.enter("tableDelimiterFiller"),function n(t){return 45===t?(e.consume(t),n):58===t?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),x):(e.exit("tableDelimiterFiller"),x(t))}(n)):t(n)}function x(n){return(0,i.On)(n)?(0,v.N)(e,g,"whitespace")(n):g(n)}function g(u){if(124===u)return d(u);if(null===u||(0,i.HP)(u))return r&&o===c?(e.exit("tableDelimiterRow"),e.exit("tableHead"),n(u)):t(u);return t(u)}function b(n){return e.enter("tableRow"),y(n)}function y(t){return 124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),y):null===t||(0,i.HP)(t)?(e.exit("tableRow"),n(t)):(0,i.On)(t)?(0,v.N)(e,y,"whitespace")(t):(e.enter("data"),S(t))}function S(n){return null===n||124===n||(0,i.Ee)(n)?(e.exit("data"),y(n)):(e.consume(n),92===n?F:S)}function F(n){return 92===n||124===n?(e.consume(n),S):S(n)}}function N(e,n){let t,r,i,u=-1,o=!0,c=0,l=[0,0,0,0],a=[0,0,0,0],s=!1,f=0,m=new O;for(;++u<e.length;){let d=e[u],p=d[1];"enter"===d[0]?"tableHead"===p.type?(s=!1,0!==f&&(_(m,n,f,t,r),r=void 0,f=0),t={type:"table",start:Object.assign({},p.start),end:Object.assign({},p.end)},m.add(u,0,[["enter",t,n]])):"tableRow"===p.type||"tableDelimiterRow"===p.type?(o=!0,i=void 0,l=[0,0,0,0],a=[0,u+1,0,0],s&&(s=!1,r={type:"tableBody",start:Object.assign({},p.start),end:Object.assign({},p.end)},m.add(u,0,[["enter",r,n]])),c="tableDelimiterRow"===p.type?2:r?3:1):c&&("data"===p.type||"tableDelimiterMarker"===p.type||"tableDelimiterFiller"===p.type)?(o=!1,0===a[2]&&(0!==l[1]&&(a[0]=a[1],i=V(m,n,l,c,void 0,i),l=[0,0,0,0]),a[2]=u)):"tableCellDivider"===p.type&&(o?o=!1:(0!==l[1]&&(a[0]=a[1],i=V(m,n,l,c,void 0,i)),a=[(l=a)[1],u,0,0])):"tableHead"===p.type?(s=!0,f=u):"tableRow"===p.type||"tableDelimiterRow"===p.type?(f=u,0!==l[1]?(a[0]=a[1],i=V(m,n,l,c,u,i)):0!==a[1]&&(i=V(m,n,a,c,u,i)),c=0):c&&("data"===p.type||"tableDelimiterMarker"===p.type||"tableDelimiterFiller"===p.type)&&(a[3]=u)}for(0!==f&&_(m,n,f,t,r),m.consume(n.events),u=-1;++u<n.events.length;){let e=n.events[u];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,n){let t=!1,r=[];for(;n<e.length;){let i=e[n];if(t){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[n+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[n-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(t=!0);n+=1}return r}(n.events,u))}return e}function V(e,n,t,r,i,u){0!==t[0]&&(u.end=Object.assign({},A(n.events,t[0])),e.add(t[0],0,[["exit",u,n]]));let o=A(n.events,t[1]);if(u={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(t[1],0,[["enter",u,n]]),0!==t[2]){let i=A(n.events,t[2]),u=A(n.events,t[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},u)};if(e.add(t[2],0,[["enter",o,n]]),2!==r){let r=n.events[t[2]],i=n.events[t[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",t[3]>t[2]+1){let n=t[2]+1,r=t[3]-t[2]-1;e.add(n,r,[])}}e.add(t[3]+1,0,[["exit",o,n]])}return void 0!==i&&(u.end=Object.assign({},A(n.events,i)),e.add(i,0,[["exit",u,n]]),u=void 0),u}function _(e,n,t,r,i){let u=[],o=A(n.events,t);i&&(i.end=Object.assign({},o),u.push(["exit",i,n])),r.end=Object.assign({},o),u.push(["exit",r,n]),e.add(t+1,0,u)}function A(e,n){let t=e[n],r="enter"===t[0]?"start":"end";return t[1][r]}RegExp("^"+/<(\/?)(iframe|noembed|noframes|plaintext|script|style|title|textarea|xmp)(?=[\t\n\f\r />])/gi.source,"i");let q={name:"tasklistCheck",tokenize:function(e,n,t){let r=this;return function(n){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(n),e.exit("taskListCheckMarker"),u):t(n)};function u(n){return(0,i.Ee)(n)?(e.enter("taskListCheckValueUnchecked"),e.consume(n),e.exit("taskListCheckValueUnchecked"),o):88===n||120===n?(e.enter("taskListCheckValueChecked"),e.consume(n),e.exit("taskListCheckValueChecked"),o):t(n)}function o(n){return 93===n?(e.enter("taskListCheckMarker"),e.consume(n),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),c):t(n)}function c(r){return(0,i.HP)(r)?n(r):(0,i.On)(r)?e.check({tokenize:R},n,t)(r):t(r)}}};function R(e,n,t){return(0,v.N)(e,function(e){return null===e?t(e):n(e)},"whitespace")}function W(e){return(0,r.y)([{text:d},{document:{91:{name:"gfmFootnoteDefinition",tokenize:M,continuation:{tokenize:H},exit:w}},text:{91:{name:"gfmFootnoteCall",tokenize:z},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:E,resolveTo:C}}},function(e){let n=(e||{}).singleTilde,t={name:"strikethrough",tokenize:function(e,t,r){let i=this.previous,u=this.events,o=0;return function(c){return 126===i&&"characterEscape"!==u[u.length-1][1].type?r(c):(e.enter("strikethroughSequenceTemporary"),function u(c){let l=(0,T.S)(i);if(126===c)return o>1?r(c):(e.consume(c),o++,u);if(o<2&&!n)return r(c);let a=e.exit("strikethroughSequenceTemporary"),s=(0,T.S)(c);return a._open=!s||2===s&&!!l,a._close=!l||2===l&&!!s,t(c)}(c))}},resolveAll:function(e,n){let t=-1;for(;++t<e.length;)if("enter"===e[t][0]&&"strikethroughSequenceTemporary"===e[t][1].type&&e[t][1]._close){let r=t;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[t][1].end.offset-e[t][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[t][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[t][1].end)},u={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[t][1].start)},o=[["enter",i,n],["enter",e[r][1],n],["exit",e[r][1],n],["enter",u,n]],c=n.parser.constructs.insideSpan.null;c&&(0,D.m)(o,o.length,0,(0,L.W)(c,e.slice(r+1,t),n)),(0,D.m)(o,o.length,0,[["exit",u,n],["enter",e[t][1],n],["exit",e[t][1],n],["exit",i,n]]),(0,D.m)(e,r-1,t-r+3,o),t=r+o.length-2;break}}for(t=-1;++t<e.length;)"strikethroughSequenceTemporary"===e[t][1].type&&(e[t][1].type="data");return e}};return null==n&&(n=!0),{text:{126:t},insideSpan:{null:[t]},attentionMarkers:{null:[126]}}}(e),{flow:{null:{name:"table",tokenize:I,resolveAll:N}}},{text:{91:q}}])}},94581:(e,n,t)=>{t.d(n,{N:()=>i});var r=t(12556);function i(e,n,t,i){let u=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.On)(i)?(e.enter(t),function i(c){return(0,r.On)(c)&&o++<u?(e.consume(c),i):(e.exit(t),n(c))}(i)):n(i)}}}}]);