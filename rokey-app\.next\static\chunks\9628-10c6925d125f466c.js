(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9628],{22475:(t,e,n)=>{"use strict";n.d(e,{UE:()=>ta,ll:()=>ti,rD:()=>tc,UU:()=>tl,cY:()=>tr,BN:()=>to});let i=Math.min,r=Math.max,o=Math.round,l=Math.floor,a=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function u(t,e){return"function"==typeof t?t(e):t}function s(t){return t.split("-")[0]}function d(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function h(t){return"y"===t?"height":"width"}function m(t){return["top","bottom"].includes(s(t))?"y":"x"}function g(t){return t.replace(/start|end/g,t=>f[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function y(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function v(t){let{x:e,y:n,width:i,height:r}=t;return{width:i,height:r,top:n,left:e,right:e+i,bottom:n+r,x:e,y:n}}function x(t,e,n){let i,{reference:r,floating:o}=t,l=m(e),a=p(m(e)),c=h(a),f=s(e),u="y"===l,g=r.x+r.width/2-o.width/2,w=r.y+r.height/2-o.height/2,y=r[c]/2-o[c]/2;switch(f){case"top":i={x:g,y:r.y-o.height};break;case"bottom":i={x:g,y:r.y+r.height};break;case"right":i={x:r.x+r.width,y:w};break;case"left":i={x:r.x-o.width,y:w};break;default:i={x:r.x,y:r.y}}switch(d(e)){case"start":i[a]-=y*(n&&u?-1:1);break;case"end":i[a]+=y*(n&&u?-1:1)}return i}let b=async(t,e,n)=>{let{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),c=await (null==l.isRTL?void 0:l.isRTL(e)),f=await l.getElementRects({reference:t,floating:e,strategy:r}),{x:u,y:s}=x(f,i,c),d=i,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:w,data:y,reset:v}=await m({x:u,y:s,initialPlacement:i,placement:d,strategy:r,middlewareData:p,rects:f,platform:l,elements:{reference:t,floating:e}});u=null!=g?g:u,s=null!=w?w:s,p={...p,[o]:{...p[o],...y}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(f=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:r}):v.rects),{x:u,y:s}=x(f,d,c)),n=-1)}return{x:u,y:s,placement:d,strategy:r,middlewareData:p}};async function R(t,e){var n;void 0===e&&(e={});let{x:i,y:r,platform:o,rects:l,elements:a,strategy:c}=t,{boundary:f="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=u(e,t),m=y(h),g=a[p?"floating"===d?"reference":"floating":d],w=v(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:f,rootBoundary:s,strategy:c})),x="floating"===d?{x:i,y:r,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),R=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},E=v(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:b,strategy:c}):x);return{top:(w.top-E.top+m.top)/R.y,bottom:(E.bottom-w.bottom+m.bottom)/R.y,left:(w.left-E.left+m.left)/R.x,right:(E.right-w.right+m.right)/R.x}}async function E(t,e){let{placement:n,platform:i,elements:r}=t,o=await (null==i.isRTL?void 0:i.isRTL(r.floating)),l=s(n),a=d(n),c="y"===m(n),f=["left","top"].includes(l)?-1:1,p=o&&c?-1:1,h=u(e,t),{mainAxis:g,crossAxis:w,alignmentAxis:y}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof y&&(w="end"===a?-1*y:y),c?{x:w*p,y:g*f}:{x:g*f,y:w*p}}function L(){return"undefined"!=typeof window}function T(t){return O(t)?(t.nodeName||"").toLowerCase():"#document"}function k(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function A(t){var e;return null==(e=(O(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function O(t){return!!L()&&(t instanceof Node||t instanceof k(t).Node)}function C(t){return!!L()&&(t instanceof Element||t instanceof k(t).Element)}function S(t){return!!L()&&(t instanceof HTMLElement||t instanceof k(t).HTMLElement)}function D(t){return!!L()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof k(t).ShadowRoot)}function F(t){let{overflow:e,overflowX:n,overflowY:i,display:r}=M(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(r)}function P(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function W(t){let e=H(),n=C(t)?M(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function H(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(t){return["html","body","#document"].includes(T(t))}function M(t){return k(t).getComputedStyle(t)}function j(t){return C(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function V(t){if("html"===T(t))return t;let e=t.assignedSlot||t.parentNode||D(t)&&t.host||A(t);return D(e)?e.host:e}function N(t,e,n){var i;void 0===e&&(e=[]),void 0===n&&(n=!0);let r=function t(e){let n=V(e);return B(n)?e.ownerDocument?e.ownerDocument.body:e.body:S(n)&&F(n)?n:t(n)}(t),o=r===(null==(i=t.ownerDocument)?void 0:i.body),l=k(r);if(o){let t=_(l);return e.concat(l,l.visualViewport||[],F(r)?r:[],t&&n?N(t):[])}return e.concat(r,N(r,[],n))}function _(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function z(t){let e=M(t),n=parseFloat(e.width)||0,i=parseFloat(e.height)||0,r=S(t),l=r?t.offsetWidth:n,a=r?t.offsetHeight:i,c=o(n)!==l||o(i)!==a;return c&&(n=l,i=a),{width:n,height:i,$:c}}function Z(t){return C(t)?t:t.contextElement}function I(t){let e=Z(t);if(!S(e))return a(1);let n=e.getBoundingClientRect(),{width:i,height:r,$:l}=z(e),c=(l?o(n.width):n.width)/i,f=(l?o(n.height):n.height)/r;return c&&Number.isFinite(c)||(c=1),f&&Number.isFinite(f)||(f=1),{x:c,y:f}}let U=a(0);function q(t){let e=k(t);return H()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:U}function Y(t,e,n,i){var r;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=Z(t),c=a(1);e&&(i?C(i)&&(c=I(i)):c=I(t));let f=(void 0===(r=n)&&(r=!1),i&&(!r||i===k(l))&&r)?q(l):a(0),u=(o.left+f.x)/c.x,s=(o.top+f.y)/c.y,d=o.width/c.x,p=o.height/c.y;if(l){let t=k(l),e=i&&C(i)?k(i):i,n=t,r=_(n);for(;r&&i&&e!==n;){let t=I(r),e=r.getBoundingClientRect(),i=M(r),o=e.left+(r.clientLeft+parseFloat(i.paddingLeft))*t.x,l=e.top+(r.clientTop+parseFloat(i.paddingTop))*t.y;u*=t.x,s*=t.y,d*=t.x,p*=t.y,u+=o,s+=l,r=_(n=k(r))}}return v({width:d,height:p,x:u,y:s})}function X(t,e){let n=j(t).scrollLeft;return e?e.left+n:Y(A(t)).left+n}function $(t,e,n){void 0===n&&(n=!1);let i=t.getBoundingClientRect();return{x:i.left+e.scrollLeft-(n?0:X(t,i)),y:i.top+e.scrollTop}}function G(t,e,n){let i;if("viewport"===e)i=function(t,e){let n=k(t),i=A(t),r=n.visualViewport,o=i.clientWidth,l=i.clientHeight,a=0,c=0;if(r){o=r.width,l=r.height;let t=H();(!t||t&&"fixed"===e)&&(a=r.offsetLeft,c=r.offsetTop)}return{width:o,height:l,x:a,y:c}}(t,n);else if("document"===e)i=function(t){let e=A(t),n=j(t),i=t.ownerDocument.body,o=r(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),l=r(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight),a=-n.scrollLeft+X(t),c=-n.scrollTop;return"rtl"===M(i).direction&&(a+=r(e.clientWidth,i.clientWidth)-o),{width:o,height:l,x:a,y:c}}(A(t));else if(C(e))i=function(t,e){let n=Y(t,!0,"fixed"===e),i=n.top+t.clientTop,r=n.left+t.clientLeft,o=S(t)?I(t):a(1),l=t.clientWidth*o.x,c=t.clientHeight*o.y;return{width:l,height:c,x:r*o.x,y:i*o.y}}(e,n);else{let n=q(t);i={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return v(i)}function J(t){return"static"===M(t).position}function K(t,e){if(!S(t)||"fixed"===M(t).position)return null;if(e)return e(t);let n=t.offsetParent;return A(t)===n&&(n=n.ownerDocument.body),n}function Q(t,e){let n=k(t);if(P(t))return n;if(!S(t)){let e=V(t);for(;e&&!B(e);){if(C(e)&&!J(e))return e;e=V(e)}return n}let i=K(t,e);for(;i&&["table","td","th"].includes(T(i))&&J(i);)i=K(i,e);return i&&B(i)&&J(i)&&!W(i)?n:i||function(t){let e=V(t);for(;S(e)&&!B(e);){if(W(e))return e;if(P(e))break;e=V(e)}return null}(t)||n}let tt=async function(t){let e=this.getOffsetParent||Q,n=this.getDimensions,i=await n(t.floating);return{reference:function(t,e,n){let i=S(e),r=A(e),o="fixed"===n,l=Y(t,!0,o,e),c={scrollLeft:0,scrollTop:0},f=a(0);if(i||!i&&!o)if(("body"!==T(e)||F(r))&&(c=j(e)),i){let t=Y(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else r&&(f.x=X(r));o&&!i&&r&&(f.x=X(r));let u=!r||i||o?a(0):$(r,c);return{x:l.left+c.scrollLeft-f.x-u.x,y:l.top+c.scrollTop-f.y-u.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},te={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:i,strategy:r}=t,o="fixed"===r,l=A(i),c=!!e&&P(e.floating);if(i===l||c&&o)return n;let f={scrollLeft:0,scrollTop:0},u=a(1),s=a(0),d=S(i);if((d||!d&&!o)&&(("body"!==T(i)||F(l))&&(f=j(i)),S(i))){let t=Y(i);u=I(i),s.x=t.x+i.clientLeft,s.y=t.y+i.clientTop}let p=!l||d||o?a(0):$(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+s.x+p.x,y:n.y*u.y-f.scrollTop*u.y+s.y+p.y}},getDocumentElement:A,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:l}=t,a=[..."clippingAncestors"===n?P(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let i=N(t,[],!1).filter(t=>C(t)&&"body"!==T(t)),r=null,o="fixed"===M(t).position,l=o?V(t):t;for(;C(l)&&!B(l);){let e=M(l),n=W(l);n||"fixed"!==e.position||(r=null),(o?!n&&!r:!n&&"static"===e.position&&!!r&&["absolute","fixed"].includes(r.position)||F(l)&&!n&&function t(e,n){let i=V(e);return!(i===n||!C(i)||B(i))&&("fixed"===M(i).position||t(i,n))}(t,l))?i=i.filter(t=>t!==l):r=e,l=V(l)}return e.set(t,i),i}(e,this._c):[].concat(n),o],c=a[0],f=a.reduce((t,n)=>{let o=G(e,n,l);return t.top=r(o.top,t.top),t.right=i(o.right,t.right),t.bottom=i(o.bottom,t.bottom),t.left=r(o.left,t.left),t},G(e,c,l));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}},getOffsetParent:Q,getElementRects:tt,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=z(t);return{width:e,height:n}},getScale:I,isElement:C,isRTL:function(t){return"rtl"===M(t).direction}};function tn(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function ti(t,e,n,o){let a;void 0===o&&(o={});let{ancestorScroll:c=!0,ancestorResize:f=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=o,p=Z(t),h=c||f?[...p?N(p):[],...N(e)]:[];h.forEach(t=>{c&&t.addEventListener("scroll",n,{passive:!0}),f&&t.addEventListener("resize",n)});let m=p&&s?function(t,e){let n,o=null,a=A(t);function c(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return!function f(u,s){void 0===u&&(u=!1),void 0===s&&(s=1),c();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(u||e(),!m||!g)return;let w=l(h),y=l(a.clientWidth-(p+m)),v={rootMargin:-w+"px "+-y+"px "+-l(a.clientHeight-(h+g))+"px "+-l(p)+"px",threshold:r(0,i(1,s))||1},x=!0;function b(e){let i=e[0].intersectionRatio;if(i!==s){if(!x)return f();i?f(!1,i):n=setTimeout(()=>{f(!1,1e-7)},1e3)}1!==i||tn(d,t.getBoundingClientRect())||f(),x=!1}try{o=new IntersectionObserver(b,{...v,root:a.ownerDocument})}catch(t){o=new IntersectionObserver(b,v)}o.observe(t)}(!0),c}(p,n):null,g=-1,w=null;u&&(w=new ResizeObserver(t=>{let[i]=t;i&&i.target===p&&w&&(w.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=w)||t.observe(e)})),n()}),p&&!d&&w.observe(p),w.observe(e));let y=d?Y(t):null;return d&&function e(){let i=Y(t);y&&!tn(y,i)&&n(),y=i,a=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{c&&t.removeEventListener("scroll",n),f&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(a)}}let tr=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,i;let{x:r,y:o,placement:l,middlewareData:a}=e,c=await E(e,t);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(i=a.arrow)&&i.alignmentOffset?{}:{x:r+c.x,y:o+c.y,data:{...c,placement:l}}}}},to=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:o,placement:l}=e,{mainAxis:a=!0,crossAxis:c=!1,limiter:f={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...d}=u(t,e),h={x:n,y:o},g=await R(e,d),w=m(s(l)),y=p(w),v=h[y],x=h[w];if(a){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=v+g[t],o=v-g[e];v=r(n,i(v,o))}if(c){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=x+g[t],o=x-g[e];x=r(n,i(x,o))}let b=f.fn({...e,[y]:v,[w]:x});return{...b,data:{x:b.x-n,y:b.y-o,enabled:{[y]:a,[w]:c}}}}}},tl=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,i,r,o,l;let{placement:a,middlewareData:c,rects:f,initialPlacement:y,platform:v,elements:x}=e,{mainAxis:b=!0,crossAxis:E=!0,fallbackPlacements:L,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:A=!0,...O}=u(t,e);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let C=s(a),S=m(y),D=s(y)===y,F=await (null==v.isRTL?void 0:v.isRTL(x.floating)),P=L||(D||!A?[w(y)]:function(t){let e=w(t);return[g(t),e,g(e)]}(y)),W="none"!==k;!L&&W&&P.push(...function(t,e,n,i){let r=d(t),o=function(t,e,n){let i=["left","right"],r=["right","left"];switch(t){case"top":case"bottom":if(n)return e?r:i;return e?i:r;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(s(t),"start"===n,i);return r&&(o=o.map(t=>t+"-"+r),e&&(o=o.concat(o.map(g)))),o}(y,A,k,F));let H=[y,...P],B=await R(e,O),M=[],j=(null==(i=c.flip)?void 0:i.overflows)||[];if(b&&M.push(B[C]),E){let t=function(t,e,n){void 0===n&&(n=!1);let i=d(t),r=p(m(t)),o=h(r),l="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(a,f,F);M.push(B[t[0]],B[t[1]])}if(j=[...j,{placement:a,overflows:M}],!M.every(t=>t<=0)){let t=((null==(r=c.flip)?void 0:r.index)||0)+1,e=H[t];if(e&&("alignment"!==E||S===m(e)||j.every(t=>t.overflows[0]>0&&m(t.placement)===S)))return{data:{index:t,overflows:j},reset:{placement:e}};let n=null==(o=j.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(T){case"bestFit":{let t=null==(l=j.filter(t=>{if(W){let e=m(t.placement);return e===S||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=y}if(a!==n)return{reset:{placement:n}}}return{}}}},ta=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:o,placement:l,rects:a,platform:c,elements:f,middlewareData:s}=e,{element:g,padding:w=0}=u(t,e)||{};if(null==g)return{};let v=y(w),x={x:n,y:o},b=p(m(l)),R=h(b),E=await c.getDimensions(g),L="y"===b,T=L?"clientHeight":"clientWidth",k=a.reference[R]+a.reference[b]-x[b]-a.floating[R],A=x[b]-a.reference[b],O=await (null==c.getOffsetParent?void 0:c.getOffsetParent(g)),C=O?O[T]:0;C&&await (null==c.isElement?void 0:c.isElement(O))||(C=f.floating[T]||a.floating[R]);let S=C/2-E[R]/2-1,D=i(v[L?"top":"left"],S),F=i(v[L?"bottom":"right"],S),P=C-E[R]-F,W=C/2-E[R]/2+(k/2-A/2),H=r(D,i(W,P)),B=!s.arrow&&null!=d(l)&&W!==H&&a.reference[R]/2-(W<D?D:F)-E[R]/2<0,M=B?W<D?W-D:W-P:0;return{[b]:x[b]+M,data:{[b]:H,centerOffset:W-H-M,...B&&{alignmentOffset:M}},reset:B}}}),tc=(t,e,n)=>{let i=new Map,r={platform:te,...n},o={...r.platform,_c:i};return b(t,e,{...r,platform:o})}},29300:(t,e)=>{var n;!function(){"use strict";var i={}.hasOwnProperty;function r(){for(var t="",e=0;e<arguments.length;e++){var n=arguments[e];n&&(t=o(t,function(t){if("string"==typeof t||"number"==typeof t)return t;if("object"!=typeof t)return"";if(Array.isArray(t))return r.apply(null,t);if(t.toString!==Object.prototype.toString&&!t.toString.toString().includes("[native code]"))return t.toString();var e="";for(var n in t)i.call(t,n)&&t[n]&&(e=o(e,n));return e}(n)))}return t}function o(t,e){return e?t?t+" "+e:t+e:t}t.exports?(r.default=r,t.exports=r):void 0===(n=(function(){return r}).apply(e,[]))||(t.exports=n)}()},52589:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});var i=n(12115);let r=i.forwardRef(function(t,e){let{title:n,titleId:r,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},o),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},67695:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});var i=n(12115);let r=i.forwardRef(function(t,e){let{title:n,titleId:r,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},o),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))})},89959:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});var i=n(12115);let r=i.forwardRef(function(t,e){let{title:n,titleId:r,...o}=t;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},o),n?i.createElement("title",{id:r},n):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})}}]);