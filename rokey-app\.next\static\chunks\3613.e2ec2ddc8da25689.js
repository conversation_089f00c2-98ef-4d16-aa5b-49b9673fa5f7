(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3613],{1969:(e,t,n)=>{"use strict";var r=n(66384);e.exports=function(e,t){return r(e,t.toLowerCase())}},11566:(e,t,n)=>{"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(e,t,n){var l;return(l=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=r(l))return l;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==r(l)?l:l+"")in e)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{A:()=>l})},20020:(e,t,n)=>{"use strict";var r=n(75870),l=n(93361)(r,"div");l.displayName="html",e.exports=l},26193:(e,t)=>{"use strict";t.q=function(e){for(var t,n=[],r=String(e||""),l=r.indexOf(","),a=0,o=!1;!o;)-1===l&&(l=r.length,o=!0),((t=r.slice(a,l).trim())||!o)&&n.push(t),a=l+1,l=r.indexOf(",",a);return n}},34093:(e,t,n)=>{"use strict";function r(){}function l(){}n.d(t,{HB:()=>l,ok:()=>r})},36301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,l=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,u=/^[;\s]*/,i=/^\s+|\s+$/g;function s(e){return e?e.replace(i,""):""}e.exports=function(e,i){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];i=i||{};var c=1,p=1;function f(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");p=~r?e.length-r:p+e.length}function d(){var e={line:c,column:p};return function(t){return t.position=new m(e),g(r),t}}function m(e){this.start=e,this.end={line:c,column:p},this.source=i.source}m.prototype.content=e;var y=[];function h(t){var n=Error(i.source+":"+c+":"+p+": "+t);if(n.reason=t,n.filename=i.source,n.line=c,n.column=p,n.source=e,i.silent)y.push(n);else throw n}function g(t){var n=t.exec(e);if(n){var r=n[0];return f(r),e=e.slice(r.length),n}}function v(e){var t;for(e=e||[];t=b();)!1!==t&&e.push(t);return e}function b(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return h("End of comment missing");var r=e.slice(2,n-2);return p+=2,f(r),e=e.slice(n),p+=2,t({type:"comment",comment:r})}}g(r);var x,S=[];for(v(S);x=function(){var e=d(),n=g(l);if(n){if(b(),!g(a))return h("property missing ':'");var r=g(o),i=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return g(u),i}}();)!1!==x&&(S.push(x),v(S));return S}},37380:(e,t,n)=>{"use strict";e.exports=n(63916)({space:"xml",transform:function(e,t){return"xml:"+t.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}})},41818:(e,t,n)=>{"use strict";function r(e){if(e)throw e}n.d(t,{V:()=>r})},41974:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,l=n.indexOf(t);for(;-1!==l;)r++,l=n.indexOf(t,l+t.length);return r}n.d(t,{D:()=>r})},43828:(e,t,n)=>{"use strict";n.d(t,{s:()=>l});let r=document.createElement("i");function l(e){let t="&"+e+";";r.innerHTML=t;let n=r.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}},46039:(e,t,n)=>{"use strict";e.exports=n(20020)},52104:(e,t,n)=>{"use strict";var r=n(9999),l=n(71747);e.exports=function(e){for(var t,n,a=e.length,o=[],u=[],i=-1;++i<a;)t=e[i],o.push(t.property),u.push(t.normal),n=t.space;return new l(r.apply(null,o),r.apply(null,u),n)}},52673:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n,r,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(l[n]=e[n])}return l}n.d(t,{A:()=>r})},53360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,l=Object.getOwnPropertyDescriptor,a=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,l=t.call(e,"constructor"),a=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!l&&!a)return!1;for(r in e);return void 0===r||t.call(e,r)},u=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},i=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(l)return l(e,n).value}return e[n]};e.exports=function e(){var t,n,r,l,s,c,p=arguments[0],f=1,d=arguments.length,m=!1;for("boolean"==typeof p&&(m=p,p=arguments[1]||{},f=2),(null==p||"object"!=typeof p&&"function"!=typeof p)&&(p={});f<d;++f)if(t=arguments[f],null!=t)for(n in t)r=i(p,n),p!==(l=i(t,n))&&(m&&l&&(o(l)||(s=a(l)))?(s?(s=!1,c=r&&a(r)?r:[]):c=r&&o(r)?r:{},u(p,{name:n,newValue:e(m,c,l)})):void 0!==l&&u(p,{name:n,newValue:l}));return p}},53538:(e,t,n)=>{"use strict";var r=n(62533),l=n(63916),a=r.booleanish,o=r.number,u=r.spaceSeparated;e.exports=l({transform:function(e,t){return"role"===t?t:"aria-"+t.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:a,ariaAutoComplete:null,ariaBusy:a,ariaChecked:a,ariaColCount:o,ariaColIndex:o,ariaColSpan:o,ariaControls:u,ariaCurrent:null,ariaDescribedBy:u,ariaDetails:null,ariaDisabled:a,ariaDropEffect:u,ariaErrorMessage:null,ariaExpanded:a,ariaFlowTo:u,ariaGrabbed:a,ariaHasPopup:null,ariaHidden:a,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:u,ariaLevel:o,ariaLive:null,ariaModal:a,ariaMultiLine:a,ariaMultiSelectable:a,ariaOrientation:null,ariaOwns:u,ariaPlaceholder:null,ariaPosInSet:o,ariaPressed:a,ariaReadOnly:a,ariaRelevant:null,ariaRequired:a,ariaRoleDescription:u,ariaRowCount:o,ariaRowIndex:o,ariaRowSpan:o,ariaSelected:a,ariaSetSize:o,ariaSort:null,ariaValueMax:o,ariaValueMin:o,ariaValueNow:o,ariaValueText:null,role:null}})},53771:(e,t,n)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function l(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,void 0);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,void 0):void 0}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,{A:()=>l})},55160:e=>{"use strict";e.exports=n;var t=n.prototype;function n(e,t){this.property=e,this.attribute=t}t.space=null,t.attribute=null,t.property=null,t.boolean=!1,t.booleanish=!1,t.overloadedBoolean=!1,t.number=!1,t.commaSeparated=!1,t.spaceSeparated=!1,t.commaOrSpaceSeparated=!1,t.mustUseProperty=!1,t.defined=!1},57052:(e,t,n)=>{"use strict";var r=n(87458),l=n(74992),a=n(55160),o="data";e.exports=function(e,t){var n,f,d,m=r(t),y=t,h=a;return m in e.normal?e.property[e.normal[m]]:(m.length>4&&m.slice(0,4)===o&&u.test(t)&&("-"===t.charAt(4)?y=o+(n=t.slice(5).replace(i,p)).charAt(0).toUpperCase()+n.slice(1):(d=(f=t).slice(4),t=i.test(d)?f:("-"!==(d=d.replace(s,c)).charAt(0)&&(d="-"+d),o+d)),h=l),new h(y,t))};var u=/^data[-\w.:]+$/i,i=/-[a-z]/g,s=/[A-Z]/g;function c(e){return"-"+e.toLowerCase()}function p(e){return e.charAt(1).toUpperCase()}},62533:(e,t)=>{"use strict";var n=0;function r(){return Math.pow(2,++n)}t.boolean=r(),t.booleanish=r(),t.overloadedBoolean=r(),t.number=r(),t.spaceSeparated=r(),t.commaSeparated=r(),t.commaOrSpaceSeparated=r()},63916:(e,t,n)=>{"use strict";var r=n(87458),l=n(71747),a=n(74992);e.exports=function(e){var t,n,o=e.space,u=e.mustUseProperty||[],i=e.attributes||{},s=e.properties,c=e.transform,p={},f={};for(t in s)n=new a(t,c(i,t),s[t],o),-1!==u.indexOf(t)&&(n.mustUseProperty=!0),p[t]=n,f[r(t)]=t,f[r(n.attribute)]=t;return new l(p,f,o)}},66384:e=>{"use strict";e.exports=function(e,t){return t in e?e[t]:t}},68055:(e,t,n)=>{"use strict";e.exports=n(63916)({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:n(1969),properties:{xmlns:null,xmlnsXLink:null}})},70275:(e,t,n)=>{"use strict";n.d(t,{$:()=>r});let r={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]}},71747:e=>{"use strict";e.exports=n;var t=n.prototype;function n(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}t.space=null,t.normal={},t.property={}},74992:(e,t,n)=>{"use strict";var r=n(55160),l=n(62533);e.exports=u,u.prototype=new r,u.prototype.defined=!0;var a=["boolean","booleanish","overloadedBoolean","number","commaSeparated","spaceSeparated","commaOrSpaceSeparated"],o=a.length;function u(e,t,n,u){var i,s,c,p,f,d,m=-1;for(i=this,(s=u)&&(i.space=s),r.call(this,e,t);++m<o;){c=this,p=d=a[m],(f=(n&l[d])===l[d])&&(c[p]=f)}}},75870:(e,t,n)=>{"use strict";var r=n(52104),l=n(79637);e.exports=r([n(37380),l,n(68055),n(53538),n(90914)])},78049:e=>{"use strict";e.exports=function(e,n){for(var r,l,a,o=e||"",u=n||"div",i={},s=0;s<o.length;)t.lastIndex=s,a=t.exec(o),(r=o.slice(s,a?a.index:o.length))&&(l?"#"===l?i.id=r:i.className?i.className.push(r):i.className=[r]:u=r,s+=r.length),a&&(l=a[0],s++);return{type:"element",tagName:u,properties:i,children:[]}};var t=/[#.]/g},79630:(e,t,n)=>{"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{A:()=>r})},79637:(e,t,n)=>{"use strict";e.exports=n(63916)({space:"xlink",transform:function(e,t){return"xlink:"+t.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}})},80122:(e,t)=>{"use strict";t.q=function(e){var t=String(e||"").trim();return""===t?[]:t.split(n)};var n=/[ \t\n\r\f]+/g},87458:e=>{"use strict";e.exports=function(e){return e.toLowerCase()}},90914:(e,t,n)=>{"use strict";var r=n(62533),l=n(63916),a=n(1969),o=r.boolean,u=r.overloadedBoolean,i=r.booleanish,s=r.number,c=r.spaceSeparated,p=r.commaSeparated;e.exports=l({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:a,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:p,acceptCharset:c,accessKey:c,action:null,allow:null,allowFullScreen:o,allowPaymentRequest:o,allowUserMedia:o,alt:null,as:null,async:o,autoCapitalize:null,autoComplete:c,autoFocus:o,autoPlay:o,capture:o,charSet:null,checked:o,cite:null,className:c,cols:s,colSpan:null,content:null,contentEditable:i,controls:o,controlsList:c,coords:s|p,crossOrigin:null,data:null,dateTime:null,decoding:null,default:o,defer:o,dir:null,dirName:null,disabled:o,download:u,draggable:i,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:o,formTarget:null,headers:c,height:s,hidden:o,high:s,href:null,hrefLang:null,htmlFor:c,httpEquiv:c,id:null,imageSizes:null,imageSrcSet:p,inputMode:null,integrity:null,is:null,isMap:o,itemId:null,itemProp:c,itemRef:c,itemScope:o,itemType:c,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:o,low:s,manifest:null,max:null,maxLength:s,media:null,method:null,min:null,minLength:s,multiple:o,muted:o,name:null,nonce:null,noModule:o,noValidate:o,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextMenu:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:o,optimum:s,pattern:null,ping:c,placeholder:null,playsInline:o,poster:null,preload:null,readOnly:o,referrerPolicy:null,rel:c,required:o,reversed:o,rows:s,rowSpan:s,sandbox:c,scope:null,scoped:o,seamless:o,selected:o,shape:null,size:s,sizes:null,slot:null,span:s,spellCheck:i,src:null,srcDoc:null,srcLang:null,srcSet:p,start:s,step:null,style:null,tabIndex:s,target:null,title:null,translate:null,type:null,typeMustMatch:o,useMap:null,value:i,width:s,wrap:null,align:null,aLink:null,archive:c,axis:null,background:null,bgColor:null,border:s,borderColor:null,bottomMargin:s,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:o,declare:o,event:null,face:null,frame:null,frameBorder:null,hSpace:s,leftMargin:s,link:null,longDesc:null,lowSrc:null,marginHeight:s,marginWidth:s,noResize:o,noHref:o,noShade:o,noWrap:o,object:null,profile:null,prompt:null,rev:null,rightMargin:s,rules:null,scheme:null,scrolling:i,standby:null,summary:null,text:null,topMargin:s,valueType:null,version:null,vAlign:null,vLink:null,vSpace:s,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:o,disableRemotePlayback:o,prefix:null,property:null,results:s,security:null,unselectable:null}})},93361:(e,t,n)=>{"use strict";var r=n(57052),l=n(87458),a=n(78049),o=n(80122).q,u=n(26193).q;e.exports=function(e,t,n){var l=n?function(e){for(var t,n=e.length,r=-1,l={};++r<n;)l[(t=e[r]).toLowerCase()]=t;return l}(n):null;return function(n,c){var p,f,d,m,y,h,g=a(n,t),v=Array.prototype.slice.call(arguments,2),b=g.tagName.toLowerCase();if(g.tagName=l&&i.call(l,b)?l[b]:b,c&&(p=c,f=g,"string"==typeof p||"length"in p||(d=f.tagName,y=(m=p).type,"input"!==d&&y&&"string"==typeof y&&("object"==typeof m.children&&"length"in m.children||((y=y.toLowerCase(),"button"===d)?"menu"!==y&&"submit"!==y&&"reset"!==y&&"button"!==y:"value"in m))))&&(v.unshift(c),c=null),c)for(h in c)!function(t,n,l){var a,i,c;null!=l&&l==l&&(i=(a=r(e,n)).property,"string"==typeof(c=l)&&(a.spaceSeparated?c=o(c):a.commaSeparated?c=u(c):a.commaOrSpaceSeparated&&(c=o(u(c).join(" ")))),"style"===i&&"string"!=typeof l&&(c=function(e){var t,n=[];for(t in e)n.push([t,e[t]].join(": "));return n.join("; ")}(c)),"className"===i&&t.className&&(c=t.className.concat(c)),t[i]=function(e,t,n){var r,l,a;if("object"!=typeof n||!("length"in n))return s(e,t,n);for(l=n.length,r=-1,a=[];++r<l;)a[r]=s(e,t,n[r]);return a}(a,i,c))}(g.properties,h,c[h]);return function e(t,n){var r,l;if("string"==typeof n||"number"==typeof n)return void t.push({type:"text",value:String(n)});if("object"==typeof n&&"length"in n){for(r=-1,l=n.length;++r<l;)e(t,n[r]);return}if("object"!=typeof n||!("type"in n))throw Error("Expected node, nodes, or string, got `"+n+"`");t.push(n)}(g.children,v),"template"===g.tagName&&(g.content={type:"root",children:g.children},g.children=[]),g}};var i={}.hasOwnProperty;function s(e,t,n){var r=n;return e.number||e.positiveNumber?isNaN(r)||""===r||(r=Number(r)):(e.boolean||e.overloadedBoolean)&&"string"==typeof r&&(""===r||l(n)===l(t))&&(r=!0),r}},94295:(e,t,n)=>{"use strict";n.d(t,{H:()=>S});var r=n(34093);let l=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,o={};function u(e,t){return((t||o).jsx?a:l).test(e)}let i=/[ \t\n\f\r]/g;function s(e){return""===e.replace(i,"")}var c=n(63771),p=n(14947),f=n(53724),d=n(2288),m=n(40098);let y={}.hasOwnProperty,h=new Map,g=/[A-Z]/g,v=new Set(["table","tbody","thead","tfoot","tr"]),b=new Set(["td","th"]),x="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function S(e,t){var n,r,l,a,o;let u;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let i=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=i,r=t.jsxDEV,u=function(e,t,l,a){let o=Array.isArray(l.children),u=(0,d.PW)(e);return r(t,l,a,o,{columnNumber:u?u.column-1:void 0,fileName:n,lineNumber:u?u.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");l=0,a=t.jsx,o=t.jsxs,u=function(e,t,n,r){let l=Array.isArray(n.children)?o:a;return r?l(t,n,r):l(t,n)}}let s={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:u,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:i,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?c.JW:c.qy,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},p=w(s,e,void 0);return p&&"string"!=typeof p?p:s.create(e,s.Fragment,{children:p||void 0},void 0)}function w(e,t,n){var l;return"element"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=c.JW),e.ancestors.push(t);let a=P(e,t.tagName,!1),o=function(e,t){let n,r,l={};for(r in t.properties)if("children"!==r&&y.call(t.properties,r)){let a=function(e,t,n){let r=(0,c.I6)(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):(0,p.A)(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return f(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new m.o("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=x+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)y.call(e,t)&&(n[function(e){let t=e.replace(g,L);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?c.HO[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(a){let[r,o]=a;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&b.has(t.tagName)?n=o:l[r]=o}}return n&&((l.style||(l.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),l}(e,t),u=A(e,t);return v.has(t.tagName)&&(u=u.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&s(e.value):s(e))})),C(e,o,a,t),E(o,u),e.ancestors.pop(),e.schema=r,e.create(t,a,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,r.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}j(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let l=e.schema,a=l;"svg"===t.name&&"html"===l.space&&(e.schema=c.JW),e.ancestors.push(t);let o=null===t.name?e.Fragment:P(e,t.name,!0),u=function(e,t){let n={};for(let l of t.attributes)if("mdxJsxExpressionAttribute"===l.type)if(l.data&&l.data.estree&&e.evaluater){let t=l.data.estree.body[0];(0,r.ok)("ExpressionStatement"===t.type);let a=t.expression;(0,r.ok)("ObjectExpression"===a.type);let o=a.properties[0];(0,r.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else j(e,t.position);else{let a,o=l.name;if(l.value&&"object"==typeof l.value)if(l.value.data&&l.value.data.estree&&e.evaluater){let t=l.value.data.estree.body[0];(0,r.ok)("ExpressionStatement"===t.type),a=e.evaluater.evaluateExpression(t.expression)}else j(e,t.position);else a=null===l.value||l.value;n[o]=a}return n}(e,t),i=A(e,t);return C(e,u,o,t),E(u,i),e.ancestors.pop(),e.schema=l,e.create(t,o,u,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);j(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return E(r,A(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(l=0,t.value):void 0}function C(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function E(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function A(e,t){let n=[],r=-1,l=e.passKeys?new Map:h;for(;++r<t.children.length;){let a,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=l.get(e)||0;a=e+"-"+t,l.set(e,t+1)}}let u=w(e,o,a);void 0!==u&&n.push(u)}return n}function P(e,t,n){let l;if(n)if(t.includes(".")){let e,n=t.split("."),a=-1;for(;++a<n.length;){let t=u(n[a])?{type:"Identifier",name:n[a]}:{type:"Literal",value:n[a]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(a&&"Literal"===t.type),optional:!1}:t}(0,r.ok)(e,"always a result"),l=e}else l=u(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else l={type:"Literal",value:t};if("Literal"===l.type){let t=l.value;return y.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(l);j(e)}function j(e,t){let n=new m.o("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=x+"#cannot-handle-mdx-estrees-without-createevaluater",n}function L(e){return"-"+e.toLowerCase()}}}]);