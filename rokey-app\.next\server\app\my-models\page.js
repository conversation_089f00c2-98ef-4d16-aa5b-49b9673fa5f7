(()=>{var e={};e.id=5690,e.ids=[5690],e.modules={192:(e,s,r)=>{Promise.resolve().then(r.bind(r,39482))},2969:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});var t=r(43210);let a=t.forwardRef(function({title:e,titleId:s,...r},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},r),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9776:(e,s,r)=>{"use strict";r.d(s,{B0:()=>a,F6:()=>i});var t=r(60687);function a({className:e=""}){return(0,t.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function i({rows:e=5,columns:s=4}){return(0,t.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:e}).map((e,r)=>(0,t.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},r))]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13344:(e,s,r)=>{Promise.resolve().then(r.bind(r,69188))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39482:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(71031),l=r(97450),d=r(2969),c=r(71178),m=r(26403),p=r(50181),x=r(20404),u=r(9776),h=r(5097);function g(){let[e,s]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[g,f]=(0,a.useState)(null),[y,v]=(0,a.useState)(""),[b,j]=(0,a.useState)(!1),N=(0,x.Z)(),[w,A]=(0,a.useState)(!1),{createHoverPrefetch:C,prefetchManageKeysData:k}=(0,h._)(),P=async()=>{i(!0),f(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configurations")}let r=await e.json();s(r)}catch(e){f(e.message)}finally{i(!1)}},q=async e=>{if(e.preventDefault(),!y.trim())return void f("Configuration name cannot be empty.");j(!0),f(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:y})}),s=await e.json();if(!e.ok)throw Error(s.details||s.error||"Failed to create configuration");v(""),A(!1),await P()}catch(e){f(e.message)}finally{j(!1)}},M=(e,s)=>{N.showConfirmation({title:"Delete Configuration",message:`Are you sure you want to delete "${s}"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.`,confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{f(null);try{let s=await fetch(`/api/custom-configs/${e}`,{method:"DELETE"}),r=await s.json();if(!s.ok)throw Error(r.details||r.error||"Failed to delete configuration");await P()}catch(e){throw f(`Failed to delete: ${e.message}`),e}})};return(0,t.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,t.jsxs)("button",{onClick:()=>A(!w),className:w?"btn-secondary":"btn-primary",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),w?"Cancel":"Create New Model"]})]}),g&&(0,t.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("p",{className:"text-red-800",children:g})]})}),w&&(0,t.jsxs)("div",{className:"card max-w-md animate-scale-in p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),(0,t.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),(0,t.jsx)("input",{type:"text",id:"configName",value:y,onChange:e=>v(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),(0,t.jsx)("button",{type:"submit",disabled:b,className:"btn-primary w-full",children:b?"Creating...":"Create Model"})]})]}),r&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,s)=>(0,t.jsx)(u.B0,{},s))}),!r&&!e.length&&!g&&!w&&(0,t.jsx)("div",{className:"card text-center py-12",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,t.jsx)(l.A,{className:"h-8 w-8 text-orange-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,t.jsxs)("button",{onClick:()=>A(!0),className:"btn-primary",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})]})}),!r&&e.length>0&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,s)=>(0,t.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:`${100*s}ms`},children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:(0,t.jsx)(l.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,t.jsx)(n(),{href:`/my-models/${e.id}`,className:"flex-1",...C(e.id),children:(0,t.jsxs)("button",{className:"btn-primary w-full",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,t.jsxs)("button",{onClick:()=>M(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,t.jsx)(p.A,{isOpen:N.isOpen,onClose:N.hideConfirmation,onConfirm:N.onConfirm,title:N.title,message:N.message,confirmText:N.confirmText,cancelText:N.cancelText,type:N.type,isLoading:N.isLoading})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69188:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89507:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["my-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69188)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-models/page",pathname:"/my-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,7482,4912,453],()=>r(89507));module.exports=t})();