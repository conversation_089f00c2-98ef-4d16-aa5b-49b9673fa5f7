(()=>{var e={};e.id=4606,e.ids=[4606],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79181:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var n=r(96559),a=r(48088),i=r(37719),o=r(32190),c=r(39398);async function u(e){try{let t=e.headers.get("x-admin-key"),r=process.env.ROKEY_ADMIN_KEY||"rokey-semantic-cache-cleanup-2024";if(t!==r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=(0,c.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{data:n,error:a}=await s.from("semantic_cache").select("id, response_data").not("response_data","is",null);if(a)return o.NextResponse.json({error:"Failed to fetch cache entries"},{status:500});if(!n||0===n.length)return o.NextResponse.json({success:!0,message:"No cache entries found",deletedCount:0});let i=n.filter(e=>{let t=e.response_data;return!!t&&"object"==typeof t&&("streamed"===t.note&&1===Object.keys(t).length||!t.choices&&!t.content&&!t.message)||!1});if(0===i.length)return o.NextResponse.json({success:!0,message:"No invalid cache entries found",deletedCount:0,totalEntries:n.length});let u=i.map(e=>e.id),{error:p}=await s.from("semantic_cache").delete().in("id",u);if(p)return o.NextResponse.json({error:"Failed to delete invalid entries"},{status:500});return o.NextResponse.json({success:!0,message:`Cleaned up ${i.length} invalid cache entries`,deletedCount:i.length,totalEntries:n.length,remainingEntries:n.length-i.length})}catch(e){return o.NextResponse.json({error:"Internal server error",details:e.message},{status:500})}}async function p(e){return o.NextResponse.json({message:"Semantic Cache Cleanup Endpoint",usage:{"POST /api/admin/cleanup-semantic-cache":{description:"Clean up invalid semantic cache entries",headers:{"x-admin-key":"Required admin key for authentication"},example:'curl -X POST /api/admin/cleanup-semantic-cache -H "x-admin-key: your-admin-key"'}}})}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/cleanup-semantic-cache/route",pathname:"/api/admin/cleanup-semantic-cache",filename:"route",bundlePath:"app/api/admin/cleanup-semantic-cache/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\admin\\cleanup-semantic-cache\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:m}=d;function x(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398],()=>r(79181));module.exports=s})();