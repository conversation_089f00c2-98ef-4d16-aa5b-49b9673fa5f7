"use strict";exports.id=5563,exports.ids=[5563],exports.modules={7610:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},68589:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},95563:(e,t,r)=>{r.r(t),r.d(t,{OrchestrationCanvas:()=>h});var s=r(60687),a=r(43210),n=r.n(a),l=r(64364),o=r(68589),i=r(7610),d=r(58089);let c=({message:e})=>{var t,r;let a=e=>{switch(e){case"assignment":return(0,s.jsx)(i.A,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,s.jsx)(d.A,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,s.jsx)(i.A,{className:"w-3 h-3 text-purple-500"});default:return null}},c="moderator"===e.sender,m=(e=>{if(!e)return"from-blue-500 to-blue-600";let t=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(e.roleId);return(0,s.jsx)("div",{className:"flex justify-start mb-4",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,s.jsx)("div",{className:`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${m} flex items-center justify-center text-white shadow-sm`,children:(t=e.sender,e.roleId,"moderator"===t?(0,s.jsx)(l.A,{className:"w-4 h-4"}):(0,s.jsx)(o.A,{className:"w-4 h-4"}))}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("span",{className:`text-sm font-semibold ${c?"text-blue-700":"text-gray-700"}`,children:e.senderName}),a(e.type)&&(0,s.jsx)("div",{className:"flex items-center",children:a(e.type)}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:e.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,s.jsx)("div",{className:`inline-block px-4 py-3 rounded-2xl shadow-sm ${c?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"} ${"completion"===e.type?"border-green-200 bg-green-50":"assignment"===e.type?"border-blue-200 bg-blue-50":"handoff"===e.type?"border-purple-200 bg-purple-50":""}`,children:(0,s.jsx)("div",{className:`text-sm leading-relaxed ${c?"text-blue-900":"text-gray-800"} ${"completion"===e.type?"text-green-900":"assignment"===e.type?"text-blue-900":"handoff"===e.type?"text-purple-900":""}`,children:e.content.split("\n").map((t,r)=>(0,s.jsxs)(n().Fragment,{children:[t,r<e.content.split("\n").length-1&&(0,s.jsx)("br",{})]},r))})}),"message"!==e.type&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"assignment"===e.type?"bg-blue-100 text-blue-800":"completion"===e.type?"bg-green-100 text-green-800":"handoff"===e.type?"bg-purple-100 text-purple-800":"clarification"===e.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:["assignment"===e.type&&"\uD83D\uDCCB Task Assignment","completion"===e.type&&"✅ Task Complete","handoff"===e.type&&"\uD83D\uDD04 Handoff","clarification"===e.type&&"❓ Clarification"]})})]})]})})},m=({senderName:e,roleId:t})=>{let r=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let t=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(t),a=!t||"moderator"===t;return(0,s.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,s.jsx)("div",{className:`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${r} flex items-center justify-center text-white shadow-sm animate-pulse`,children:(e=>e&&"moderator"!==e?(0,s.jsx)(o.A,{className:"w-4 h-4"}):(0,s.jsx)(l.A,{className:"w-4 h-4"}))(t)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("span",{className:`text-sm font-semibold ${a?"text-blue-700":"text-gray-700"}`,children:e}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let t=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(e)})]}),(0,s.jsx)("div",{className:`inline-block px-4 py-3 rounded-2xl shadow-sm ${a?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"}`,children:(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})},u=({executionId:e,events:t,isConnected:r,error:n,isComplete:l})=>{let[o,i]=(0,a.useState)([]),[d,u]=(0,a.useState)(new Set),x=(0,a.useRef)(null);return((0,a.useEffect)(()=>{x.current?.scrollIntoView({behavior:"smooth"})},[o]),(0,a.useEffect)(()=>{let r=[],s=new Set;t.forEach((t,a)=>{let n=new Date(t.timestamp||Date.now()),l=`${e}-${a}`;switch(t.type){case"orchestration_started":r.push({id:l,sender:"moderator",senderName:"Moderator",content:"\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.",timestamp:n,type:"message"});break;case"task_decomposed":let o=(t.data?.steps||[]).map(e=>`🤖 @${e.roleId} - ${e.modelName||"AI Specialist"}`).join("\n");r.push({id:l,sender:"moderator",senderName:"Moderator",content:`📋 I've analyzed the task and assembled this expert team:

${o}

Let's begin the collaboration!`,timestamp:n,type:"assignment"});break;case"step_assigned":r.push({id:l,sender:"moderator",senderName:"Moderator",roleId:t.role_id,content:`🎯 @${t.role_id}, you're up! ${t.data?.commentary||"Please begin your specialized work on this task."}`,timestamp:n,type:"assignment"});break;case"moderator_assignment":r.push({id:l,sender:"moderator",senderName:"Moderator",roleId:t.role_id,content:t.data?.message||`🎯 @${t.role_id}, you're up! Please begin your specialized work on this task.`,timestamp:n,type:"assignment"});break;case"specialist_acknowledgment":r.push({id:l,sender:"specialist",senderName:t.role_id||"Specialist",roleId:t.role_id,content:t.data?.message||`✅ Understood! I'm ${t.role_id} and I'll handle this task with expertise. Starting work now...`,timestamp:n,type:"message"});break;case"step_started":case"step_progress":t.role_id&&s.add(t.role_id);break;case"specialist_message":r.push({id:l,sender:"specialist",senderName:t.role_id||"Specialist",roleId:t.role_id,content:`${t.data?.message||"\uD83C\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:"}

${t.data?.output||"Task completed successfully!"}`,timestamp:n,type:"completion"});break;case"step_completed":t.role_id&&s.delete(t.role_id);break;case"handoff_message":r.push({id:l,sender:"moderator",senderName:"Moderator",content:t.data?.message||`✨ Excellent work, @${t.data?.fromRole}! Quality looks great. Now passing to @${t.data?.toRole}...`,timestamp:n,type:"handoff"});break;case"synthesis_started":r.push({id:l,sender:"moderator",senderName:"Moderator",content:t.data?.message||`🧩 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...`,timestamp:n,type:"message"}),s.add("moderator");break;case"synthesis_complete":s.delete("moderator"),r.push({id:l,sender:"moderator",senderName:"Moderator",content:t.data?.message||`🎊 Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!`,timestamp:n,type:"completion"})}}),i(r),u(s)},[t,e]),n)?(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Connection Error"}),(0,s.jsx)("p",{className:"text-gray-600",children:n})]})}):(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:`px-4 py-2 text-xs font-medium ${r?"bg-green-50 text-green-700 border-b border-green-100":"bg-yellow-50 text-yellow-700 border-b border-yellow-100"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ${r?"bg-green-500":"bg-yellow-500"}`}),(0,s.jsx)("span",{children:r?"Connected to AI Team":"Connecting..."})]})}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===o.length&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-blue-600 animate-pulse",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,s.jsx)("p",{className:"text-gray-500",children:"Waiting for AI team to start collaboration..."})]}),o.map(e=>(0,s.jsx)(c,{message:e},e.id)),Array.from(d).map(e=>(0,s.jsx)(m,{senderName:e,roleId:"moderator"!==e?e:void 0},e)),(0,s.jsx)("div",{ref:x})]})]})};var x=r(17712);let p=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))}),h=({executionId:e,onComplete:t,onError:r,onCanvasStateChange:n,forceMaximize:o=!1})=>{let[i,d]=(0,a.useState)(!0),[c,m]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[f,b]=(0,a.useState)(""),{events:v,isConnected:y,error:j}=function(e,t){let[r,s]=(0,a.useState)([]),[n,l]=(0,a.useState)(!1),[o,i]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),[m,u]=(0,a.useState)(""),x=(0,a.useRef)(null),p=(0,a.useRef)(null);(0,a.useRef)(null);let h=(0,a.useRef)(0),g=(0,a.useRef)(""),f=(0,a.useRef)(""),b=(0,a.useCallback)(()=>{x.current&&(x.current.close(),x.current=null),p.current&&(clearTimeout(p.current),p.current=null),l(!1)},[]),v=(0,a.useCallback)(()=>{if(!e)return void i("No execution ID or direct stream URL provided");let r=t||(e?`/api/orchestration/stream/${e}`:"");if(!r)return void i("No valid stream URL could be determined");if(f.current!==r||!n){b(),g.current=e||"",f.current=r;try{let e=new EventSource(r);x.current=e,e.onopen=()=>{l(!0),i(null),h.current=0},e.onmessage=e=>{try{let t=JSON.parse(e.data);s(e=>[...e,t]),c(t),i(null)}catch(e){i("Error parsing stream data")}},e.addEventListener("orchestration_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_progress",e=>{JSON.parse(e.data)}),e.addEventListener("step_completed",e=>{JSON.parse(e.data)}),e.addEventListener("synthesis_started",e=>{JSON.parse(e.data)}),e.addEventListener("orchestration_completed",e=>{JSON.parse(e.data)}),e.onerror=e=>{if(l(!1),h.current<5){let e=1e3*Math.pow(2,h.current);h.current++,i(`Connection lost. Reconnecting in ${e/1e3}s... (attempt ${h.current}/5)`),p.current=setTimeout(()=>{v()},e)}else i("Connection failed after multiple attempts. Please refresh the page.")}}catch(e){i("Failed to establish connection"),l(!1)}}},[e,b]);return{events:r,isConnected:n,error:o,lastEvent:d,reconnect:(0,a.useCallback)(()=>{h.current=0,v()},[v]),disconnect:b}}(e);(0,a.useEffect)(()=>{let e=v.find(e=>"synthesis_complete"===e.type);if(e&&!h){g(!0);let r=e.data?.result||"Orchestration completed successfully";b(r),t&&t(r)}},[v,h,t]),(0,a.useEffect)(()=>{j&&r&&r(j)},[j,r]);let N=()=>{m(!1),d(!0),n?.(!0,!1)};return((0,a.useEffect)(()=>{n?.(i,c)},[i,c,n]),(0,a.useEffect)(()=>{o&&c&&N()},[o,c,N]),c||!i)?null:(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:`fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out ${i&&!c?"translate-x-0":"translate-x-full"}`,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none"}),(0,s.jsxs)("div",{className:"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("div",{className:"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"font-bold text-white text-lg tracking-wide",children:"AI Team Collaboration"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ${h?"bg-green-400":"bg-orange-400"} animate-pulse`}),(0,s.jsx)("p",{className:"text-sm text-gray-300 font-medium",children:h?"Mission Complete":"Team Active"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 text-orange-400"}),(0,s.jsx)("span",{className:"text-xs text-orange-300 font-medium",children:"LIVE"})]}),(0,s.jsxs)("button",{onClick:()=>{m(!0),n?.(!1,!0)},className:"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30","aria-label":"Minimize canvas",children:[(0,s.jsx)(p,{className:"w-5 h-5 transition-transform group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10"})]})]})]}),(0,s.jsxs)("div",{className:"flex-1 h-full overflow-hidden relative",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]"})]}),(0,s.jsx)(u,{executionId:e,events:v,isConnected:y,error:j,isComplete:h})]})]})})}}};