"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7096],{17096:(e,n,t)=>{t.d(n,{qg:()=>T,_K:()=>P,vk:()=>F});var r={};t.r(r),t.d(r,{attentionMarkers:()=>A,contentInitial:()=>k,disable:()=>C,document:()=>_,flow:()=>w,flowInitial:()=>b,insideSpan:()=>S,string:()=>y,text:()=>I}),t(43828);var i=t(11603),u=t(69381);t(84545),t(66089),t(33386),t(25583);var o=t(94581),l=t(12556);let s={tokenize:function(e){let n,t=e.attempt(this.parser.constructs.contentInitial,function(n){return null===n?void e.consume(n):(e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,o.N)(e,t,"linePrefix"))},function(t){return e.enter("paragraph"),function t(r){let i=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=i),n=i,function n(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,l.HP)(r)?(e.consume(r),e.exit("chunkText"),t):(e.consume(r),n)}(r)}(t)});return t}},c={tokenize:function(e){let n,t,r,u=this,o=[],s=0;return c;function c(n){if(s<o.length){let t=o[s];return u.containerState=t[1],e.attempt(t[0].continuation,a,d)(n)}return d(n)}function a(e){if(s++,u.containerState._closeFlow){let t;u.containerState._closeFlow=void 0,n&&k();let r=u.events.length,o=r;for(;o--;)if("exit"===u.events[o][0]&&"chunkFlow"===u.events[o][1].type){t=u.events[o][1].end;break}_(s);let l=r;for(;l<u.events.length;)u.events[l][1].end={...t},l++;return(0,i.m)(u.events,o+1,0,u.events.slice(r)),u.events.length=l,d(e)}return c(e)}function d(t){if(s===o.length){if(!n)return v(t);if(n.currentConstruct&&n.currentConstruct.concrete)return g(t);u.interrupt=!!(n.currentConstruct&&!n._gfmTableDynamicInterruptHack)}return u.containerState={},e.check(f,p,h)(t)}function p(e){return n&&k(),_(s),v(e)}function h(e){return u.parser.lazy[u.now().line]=s!==o.length,r=u.now().offset,g(e)}function v(n){return u.containerState={},e.attempt(f,x,g)(n)}function x(e){return s++,o.push([u.currentConstruct,u.containerState]),v(e)}function g(r){if(null===r){n&&k(),_(0),e.consume(r);return}return n=n||u.parser.flow(u.now()),e.enter("chunkFlow",{_tokenizer:n,contentType:"flow",previous:t}),function n(t){if(null===t){m(e.exit("chunkFlow"),!0),_(0),e.consume(t);return}return(0,l.HP)(t)?(e.consume(t),m(e.exit("chunkFlow")),s=0,u.interrupt=void 0,c):(e.consume(t),n)}(r)}function m(e,o){let l=u.sliceStream(e);if(o&&l.push(null),e.previous=t,t&&(t.next=e),t=e,n.defineSkip(e.start),n.write(l),u.parser.lazy[e.start.line]){let e,t,o=n.events.length;for(;o--;)if(n.events[o][1].start.offset<r&&(!n.events[o][1].end||n.events[o][1].end.offset>r))return;let l=u.events.length,c=l;for(;c--;)if("exit"===u.events[c][0]&&"chunkFlow"===u.events[c][1].type){if(e){t=u.events[c][1].end;break}e=!0}for(_(s),o=l;o<u.events.length;)u.events[o][1].end={...t},o++;(0,i.m)(u.events,c+1,0,u.events.slice(l)),u.events.length=o}}function _(n){let t=o.length;for(;t-- >n;){let n=o[t];u.containerState=n[1],n[0].exit.call(u,e)}o.length=n}function k(){n.write([null]),t=void 0,n=void 0,u.containerState._closeFlow=void 0}}},f={tokenize:function(e,n,t){return(0,o.N)(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var a=t(38736);let d={tokenize:function(e){let n=this,t=e.attempt(a.Br,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t)},e.attempt(this.parser.constructs.flowInitial,r,(0,o.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(a.Qs,r)),"linePrefix")));return t;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n.currentConstruct=void 0,t)}}},p={resolveAll:g()},h=x("string"),v=x("text");function x(e){return{resolveAll:g("text"===e?m:void 0),tokenize:function(n){let t=this,r=this.parser.constructs[e],i=n.attempt(r,u,o);return u;function u(e){return s(e)?i(e):o(e)}function o(e){return null===e?void n.consume(e):(n.enter("data"),n.consume(e),l)}function l(e){return s(e)?(n.exit("data"),i(e)):(n.consume(e),l)}function s(e){if(null===e)return!0;let n=r[e],i=-1;if(n)for(;++i<n.length;){let e=n[i];if(!e.previous||e.previous.call(t,t.previous))return!0}return!1}}}}function g(e){return function(n,t){let r,i=-1;for(;++i<=n.length;)void 0===r?n[i]&&"data"===n[i][1].type&&(r=i,i++):n[i]&&"data"===n[i][1].type||(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(n,t):n}}function m(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||"lineEnding"===e[t][1].type)&&"data"===e[t-1][1].type){let r,i=e[t-1][1],u=n.sliceStream(i),o=u.length,l=-1,s=0;for(;o--;){let e=u[o];if("string"==typeof e){for(l=e.length;32===e.charCodeAt(l-1);)s++,l--;if(l)break;l=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{o++;break}}if(n._contentTypeTextTrailing&&t===e.length&&(s=0),s){let u={type:t===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?l:i.start._bufferIndex+l,_index:i.start._index+o,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...u.start},i.start.offset===i.end.offset?Object.assign(i,u):(e.splice(t,0,["enter",u,n],["exit",u,n]),t+=2)}t++}return e}let _={42:a.p_,43:a.p_,45:a.p_,48:a.p_,49:a.p_,50:a.p_,51:a.p_,52:a.p_,53:a.p_,54:a.p_,55:a.p_,56:a.p_,57:a.p_,62:a.iZ},k={91:a.mw},b={[-2]:a.jW,[-1]:a.jW,32:a.jW},w={35:a.OS,42:a.V2,45:[a.AI,a.V2],60:a.GK,61:a.AI,95:a.V2,96:a.b8,126:a.b8},y={38:a.Ld,92:a.LU},I={[-5]:a.En,[-4]:a.En,[-3]:a.En,33:a.u4,38:a.Ld,42:a.fP,60:[a.mf,a.js],91:a.JL,92:[a.Gg,a.LU],93:a.om,95:a.fP,96:a.ph},S={null:[a.fP,p]},A={null:[42,95]},C={null:[]};var z=t(91877);function T(e){let n={constructs:(0,u.y)([r,...(e||{}).extensions||[]]),content:t(s),defined:[],document:t(c),flow:t(d),lazy:{},string:t(h),text:t(v)};return n;function t(e){return function(t){return function(e,n,t){let r={_bufferIndex:-1,_index:0,line:t&&t.line||1,column:t&&t.column||1,offset:t&&t.offset||0},u={},o=[],s=[],c=[],f={attempt:x(function(e,n){g(e,n.from)}),check:x(v),consume:function(e){(0,l.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,m()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),a.previous=e},enter:function(e,n){let t=n||{};return t.type=e,t.start=h(),a.events.push(["enter",t,a]),c.push(t),t},exit:function(e){let n=c.pop();return n.end=h(),a.events.push(["exit",n,a]),n},interrupt:x(v,{interrupt:!0})},a={code:null,containerState:{},defineSkip:function(e){u[e.line]=e.column,m()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,n){return function(e,n){let t,r=-1,i=[];for(;++r<e.length;){let u,o=e[r];if("string"==typeof o)u=o;else switch(o){case -5:u="\r";break;case -4:u="\n";break;case -3:u="\r\n";break;case -2:u=n?" ":"	";break;case -1:if(!n&&t)continue;u=" ";break;default:u=String.fromCharCode(o)}t=-2===o,i.push(u)}return i.join("")}(p(e),n)},sliceStream:p,write:function(e){return(s=(0,i.V)(s,e),function(){let e;for(;r._index<s.length;){let t=s[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;){var n;n=t.charCodeAt(r._bufferIndex),d=d(n)}else d=d(t)}}(),null!==s[s.length-1])?[]:(g(n,0),a.events=(0,z.W)(o,a.events,a),a.events)}},d=n.tokenize.call(a,f);return n.resolveAll&&o.push(n),a;function p(e){return function(e,n){let t,r=n.start._index,i=n.start._bufferIndex,u=n.end._index,o=n.end._bufferIndex;if(r===u)t=[e[r].slice(i,o)];else{if(t=e.slice(r,u),i>-1){let e=t[0];"string"==typeof e?t[0]=e.slice(i):t.shift()}o>0&&t.push(e[u].slice(0,o))}return t}(s,e)}function h(){let{_bufferIndex:e,_index:n,line:t,column:i,offset:u}=r;return{_bufferIndex:e,_index:n,line:t,column:i,offset:u}}function v(e,n){n.restore()}function x(e,n){return function(t,i,u){var o;let l,s,d,p;return Array.isArray(t)?v(t):"tokenize"in t?v([t]):(o=t,function(e){let n=null!==e&&o[e],t=null!==e&&o.null;return v([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(t)?t:t?[t]:[]])(e)});function v(e){return(l=e,s=0,0===e.length)?u:x(e[s])}function x(e){return function(t){return(p=function(){let e=h(),n=a.previous,t=a.currentConstruct,i=a.events.length,u=Array.from(c);return{from:i,restore:function(){r=e,a.previous=n,a.currentConstruct=t,a.events.length=i,c=u,m()}}}(),d=e,e.partial||(a.currentConstruct=e),e.name&&a.parser.constructs.disable.null.includes(e.name))?_(t):e.tokenize.call(n?Object.assign(Object.create(a),n):a,f,g,_)(t)}}function g(n){return e(d,p),i}function _(e){return(p.restore(),++s<l.length)?x(l[s]):u}}}function g(e,n){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,i.m)(a.events,n,a.events.length-n,e.resolve(a.events.slice(n),a)),e.resolveTo&&(a.events=e.resolveTo(a.events,a))}function m(){r.line in u&&r.column<2&&(r.column=u[r.line],r.offset+=u[r.line]-1)}}(n,e,t)}}}var E=t(58043);function P(e){for(;!(0,E.w)(e););return e}let j=/[\0\t\n\r]/g;function F(){let e,n=1,t="",r=!0;return function(i,u,o){let l,s,c,f,a,d=[];for(i=t+("string"==typeof i?i.toString():new TextDecoder(u||void 0).decode(i)),c=0,t="",r&&(65279===i.charCodeAt(0)&&c++,r=void 0);c<i.length;){if(j.lastIndex=c,f=(l=j.exec(i))&&void 0!==l.index?l.index:i.length,a=i.charCodeAt(f),!l){t=i.slice(c);break}if(10===a&&c===f&&e)d.push(-3),e=void 0;else switch(e&&(d.push(-5),e=void 0),c<f&&(d.push(i.slice(c,f)),n+=f-c),a){case 0:d.push(65533),n++;break;case 9:for(s=4*Math.ceil(n/4),d.push(-2);n++<s;)d.push(-1);break;case 10:d.push(-4),n=1;break;default:e=!0,n=1}c=f+1}return o&&(e&&d.push(-5),t&&d.push(t),d.push(null)),d}}}}]);