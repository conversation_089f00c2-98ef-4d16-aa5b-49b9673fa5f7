(()=>{var e={};e.id=9899,e.ids=[9899],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6813:(e,t,r)=>{Promise.resolve().then(r.bind(r,69195))},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var i=r(65239),s=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["routing-strategies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69195)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-strategies\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,61348)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-strategies\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\routing-strategies\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/routing-strategies/page",pathname:"/routing-strategies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61348:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>i});let i={title:"RouKey Routing Strategies - Advanced AI Gateway Routing | RouKey",description:"Explore RouKey's advanced routing strategies: Multi-Role Orchestration, Complexity-Based Routing, Smart Cost Optimization, A/B Testing, and more. Choose the perfect strategy for your AI applications.",keywords:"AI routing strategies, multi-role orchestration, complexity routing, cost optimization, A/B testing, intelligent routing, AI gateway strategies",openGraph:{title:"RouKey Routing Strategies - Advanced AI Gateway Routing",description:"Discover RouKey's revolutionary routing strategies including Multi-Role Orchestration, Smart Cost Optimization, and intelligent complexity-based routing.",type:"website",url:"https://roukey.online/routing-strategies"},alternates:{canonical:"https://roukey.online/routing-strategies"}};function s({children:e}){return e}},62261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(60687),s=r(43210),a=r(52535),o=r(57093),n=r(17457),l=r(51426),c=r(62392),d=r(74461),p=r(65443),u=r(31082),m=r(86297),x=r(7610),h=r(14689),g=r(64364),f=r(6508);let y=[{id:"none",name:"Default Load Balancing",shortDescription:"Automatic load balancing",description:"RouKey automatically load balances across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:l.A,features:["Automatic load distribution","Built-in retry mechanisms","Zero configuration required","High availability"],useCase:"Perfect for simple setups where you want reliable distribution across multiple API keys without complex routing logic.",performance:"Excellent reliability with automatic failover",complexity:"Beginner",color:"from-gray-500 to-gray-600"},{id:"intelligent_role",name:"RouKey's Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RouKey uses advanced AI to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:c.A,features:["RouKey's AI-powered classification","Dynamic role assignment","Context-aware routing","Fallback to default model"],useCase:"Ideal for applications with diverse use cases like coding, writing, analysis, and general chat.",performance:"Superior accuracy with RouKey's proprietary classification",complexity:"Intermediate",color:"from-[#ff6b35] to-[#f7931e]",featured:!0},{id:"complexity_round_robin",name:"RouKey's Complexity-Based Routing",shortDescription:"Route by prompt complexity",description:"RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:d.A,features:["Intelligent complexity analysis","Optimized model selection","Cost-performance balance","Proximal level fallback"],useCase:"Perfect for cost optimization - route simple tasks to cheaper models and complex tasks to premium models.",performance:"Optimal cost-performance ratio",complexity:"Advanced",color:"from-blue-500 to-blue-600"},{id:"strict_fallback",name:"Strict Fallback Strategy",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.",icon:p.A,features:["Predictable routing order","Guaranteed fallback chain","Manual priority control","Reliable failover"],useCase:"Best for scenarios where you have a preferred model hierarchy and want guaranteed fallback behavior.",performance:"Highly predictable with manual control",complexity:"Intermediate",color:"from-green-500 to-green-600"},{id:"cost_optimized",name:"RouKey's Smart Cost Optimization",shortDescription:"Intelligent cost-performance balance",description:"RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.",icon:u.A,features:["RouKey's learning algorithms","Dynamic cost optimization","Quality preservation","Automatic model selection"],useCase:"Essential for production applications where cost control is critical but quality cannot be compromised.",performance:"Maximum cost savings with quality assurance",complexity:"Advanced",color:"from-emerald-500 to-emerald-600",featured:!0},{id:"ab_routing",name:"RouKey's A/B Testing Router",shortDescription:"Continuous model optimization",description:"RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.",icon:m.A,features:["Continuous optimization","Data-driven decisions","Performance tracking","Automatic improvements"],useCase:"Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.",performance:"Self-improving performance over time",complexity:"Advanced",color:"from-purple-500 to-purple-600"}];function b(){let[e,t]=(0,s.useState)(y[1]);return(0,i.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,i.jsx)(o.A,{}),(0,i.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200 pt-20",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)(a.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-4xl md:text-5xl font-bold text-black mb-6",children:["RouKey's Advanced ",(0,i.jsx)("span",{className:"text-[#ff6b35]",children:"Routing Strategies"})]}),(0,i.jsx)(a.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-xl text-gray-600 max-w-3xl mx-auto mb-8",children:"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration."}),(0,i.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,i.jsxs)(f.A,{href:"/pricing",className:"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors",children:["View Pricing Plans",(0,i.jsx)(x.A,{className:"ml-2 h-5 w-5"})]})})]})})}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:y.map((r,s)=>(0,i.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},onClick:()=>t(r),className:`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${e.id===r.id?"border-[#ff6b35] bg-[#ff6b35]/5":"border-gray-200 hover:border-gray-300"}`,children:[r.featured&&(0,i.jsx)("div",{className:"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold",children:"Featured"}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:`p-3 rounded-lg bg-gradient-to-r ${r.color}`,children:(0,i.jsx)(r.icon,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"text-lg font-bold text-black mb-2",children:r.name}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:r.shortDescription}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Beginner"===r.complexity?"bg-green-100 text-green-800":"Intermediate"===r.complexity?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:r.complexity}),e.id===r.id&&(0,i.jsx)(h.A,{className:"h-5 w-5 text-[#ff6b35]"})]})]})]})]},r.id))})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)("div",{className:"sticky top-8",children:(0,i.jsxs)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"bg-white border border-gray-200 rounded-xl p-6 shadow-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,i.jsx)("div",{className:`p-3 rounded-lg bg-gradient-to-r ${e.color}`,children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-black",children:e.name}),(0,i.jsx)("p",{className:"text-gray-600",children:e.shortDescription})]})]}),(0,i.jsx)("p",{className:"text-gray-700 mb-6",children:e.description}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Key Features"}),(0,i.jsx)("ul",{className:"space-y-2",children:e.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 text-[#ff6b35]"}),(0,i.jsx)("span",{className:"text-gray-700 text-sm",children:e})]},t))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Best Use Case"}),(0,i.jsx)("p",{className:"text-gray-700 text-sm",children:e.useCase})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Performance"}),(0,i.jsx)("p",{className:"text-gray-700 text-sm",children:e.performance})]})]}),(0,i.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,i.jsxs)(f.A,{href:"/pricing",className:"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors",children:[(0,i.jsx)(g.A,{className:"mr-2 h-5 w-5"}),"Get Started"]})})]},e.id)})})]})}),(0,i.jsx)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Ready to Optimize Your AI Routing?"}),(0,i.jsx)("p",{className:"text-xl text-white/90 mb-8",children:"Start with any strategy and switch anytime. No configuration required."}),(0,i.jsxs)(f.A,{href:"/auth/signup?plan=professional",className:"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg",children:["Start Building Now",(0,i.jsx)(x.A,{className:"ml-3 h-5 w-5"})]})]})}),(0,i.jsx)(n.A,{})]})}},62392:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64364:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},65443:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))})},69195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-strategies\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},74461:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(43210);let s=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))})},76957:(e,t,r)=>{Promise.resolve().then(r.bind(r,62261))},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,7482,2535,4912,6389],()=>r(36985));module.exports=i})();