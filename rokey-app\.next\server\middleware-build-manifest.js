globalThis.__BUILD_MANIFEST={polyfillFiles:["static/chunks/polyfills-42372ed130431b0a.js"],devFiles:[],ampDevFiles:[],lowPriorityFiles:[],rootMainFiles:["static/chunks/webpack-5cecfe727564fadb.js","static/chunks/6642-580f8318c2fded3f.js","static/chunks/7706-fc32f1770db5be21.js","static/chunks/7544-9ec14bfbc636346c.js","static/chunks/2138-750ab9ae70ea69d3.js","static/chunks/4518-ca15be055cf49419.js","static/chunks/9248-bb248245e86a805e.js","static/chunks/2324-377a61cb036896c7.js","static/chunks/main-app-7603288af596d45a.js"],rootMainFilesTree:{},pages:{"/_app":["static/chunks/webpack-5cecfe727564fadb.js","static/chunks/react-b6eb68949bf517f4.js","static/chunks/1630-da514b75321b86a2.js","static/chunks/7701-79366fc10909eb39.js","static/chunks/main-754975ec0b97c024.js","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","static/chunks/vendors-ad6a2f20-92808b268818c916.js","static/chunks/vendors-04fef8b0-f66a080f2553b62a.js","static/chunks/vendors-7ec938a2-0338405445251cdc.js","static/chunks/vendors-2ced652b-8bf9d08c4789c20d.js","static/chunks/vendors-b49fab05-09e23252f7421d3c.js","static/chunks/vendors-27f02048-f9b9d680c13dc20b.js","static/chunks/vendors-fa70753b-62b027da1ccff19b.js","static/chunks/pages/_app-0beab4e43583bc3c.js"],"/_error":["static/chunks/webpack-5cecfe727564fadb.js","static/chunks/react-b6eb68949bf517f4.js","static/chunks/1630-da514b75321b86a2.js","static/chunks/7701-79366fc10909eb39.js","static/chunks/main-754975ec0b97c024.js","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","static/chunks/vendors-ad6a2f20-92808b268818c916.js","static/chunks/vendors-04fef8b0-f66a080f2553b62a.js","static/chunks/vendors-7ec938a2-0338405445251cdc.js","static/chunks/vendors-2ced652b-8bf9d08c4789c20d.js","static/chunks/vendors-b49fab05-09e23252f7421d3c.js","static/chunks/vendors-27f02048-f9b9d680c13dc20b.js","static/chunks/vendors-fa70753b-62b027da1ccff19b.js","static/chunks/pages/_error-ccf8073a28f1e494.js"]},ampFirstPages:[]},globalThis.__BUILD_MANIFEST.lowPriorityFiles=["/static/"+process.env.__NEXT_BUILD_ID+"/_buildManifest.js",,"/static/"+process.env.__NEXT_BUILD_ID+"/_ssgManifest.js"];